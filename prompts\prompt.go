package prompts

import (
	"embed"
	"fmt"
	"strings"
	"time"

	"arien-ai/config"
	"arien-ai/providers"
)

//go:embed system.md
var systemPromptFS embed.FS

// SystemPrompt manages the system prompt for the AI
type SystemPrompt struct {
	basePrompt string
	config     *config.Config
}

// NewSystemPrompt creates a new system prompt manager
func NewSystemPrompt() (*SystemPrompt, error) {
	content, err := systemPromptFS.ReadFile("system.md")
	if err != nil {
		return nil, fmt.Errorf("failed to read system prompt: %w", err)
	}

	return &SystemPrompt{
		basePrompt: string(content),
		config:     config.Get(),
	}, nil
}

// GetSystemMessage returns the complete system message for the AI
func (sp *SystemPrompt) GetSystemMessage() providers.Message {
	prompt := sp.buildPrompt()
	
	return providers.Message{
		Role:    "system",
		Content: prompt,
	}
}

// buildPrompt builds the complete system prompt with context
func (sp *SystemPrompt) buildPrompt() string {
	var builder strings.Builder
	
	// Add base system prompt
	builder.WriteString(sp.basePrompt)
	builder.WriteString("\n\n")
	
	// Add current context
	builder.WriteString("## Current Context\n\n")
	builder.WriteString(sp.buildContextSection())
	
	// Add provider-specific instructions
	builder.WriteString("\n\n## Provider-Specific Instructions\n\n")
	builder.WriteString(sp.buildProviderInstructions())
	
	// Add safety reminders
	builder.WriteString("\n\n## Safety Reminders\n\n")
	builder.WriteString(sp.buildSafetyReminders())
	
	return builder.String()
}

// buildContextSection builds the current context section
func (sp *SystemPrompt) buildContextSection() string {
	var builder strings.Builder
	
	// Current time and date
	now := time.Now()
	builder.WriteString(fmt.Sprintf("**Current Date/Time**: %s\n", 
		now.Format("Monday, January 2, 2006 at 15:04:05 MST")))
	
	// Provider and model info
	builder.WriteString(fmt.Sprintf("**LLM Provider**: %s\n", sp.config.Provider.Name))
	builder.WriteString(fmt.Sprintf("**Model**: %s\n", sp.config.Provider.Model))
	
	// Terminal settings
	builder.WriteString(fmt.Sprintf("**Auto Approve Commands**: %t\n", sp.config.Terminal.AutoApprove))
	builder.WriteString(fmt.Sprintf("**Confirm Commands**: %t\n", sp.config.Terminal.ConfirmCommands))
	
	// Session settings
	builder.WriteString(fmt.Sprintf("**Max History**: %d messages\n", sp.config.Session.MaxHistory))
	
	return builder.String()
}

// buildProviderInstructions builds provider-specific instructions
func (sp *SystemPrompt) buildProviderInstructions() string {
	switch sp.config.Provider.Name {
	case "deepseek":
		return `**Deepseek Provider Notes**:
- You have access to advanced reasoning capabilities
- Use the deepseek-reasoner model for complex problem-solving tasks
- Function calling is fully supported with proper tool definitions
- Streaming responses are available for real-time interaction`

	case "ollama":
		return `**Ollama Provider Notes**:
- You are running on a local Ollama instance
- Response times may vary based on local hardware
- Function calling support depends on the specific model being used
- Consider the local context and available system resources`

	default:
		return "**Provider**: Standard LLM provider with function calling support"
	}
}

// buildSafetyReminders builds safety reminder section
func (sp *SystemPrompt) buildSafetyReminders() string {
	var builder strings.Builder
	
	builder.WriteString("**CRITICAL SAFETY RULES**:\n\n")
	
	if sp.config.Terminal.ConfirmCommands {
		builder.WriteString("1. **ALWAYS ask for confirmation** before executing potentially destructive commands\n")
	} else {
		builder.WriteString("1. **Command confirmation is DISABLED** - be extra cautious with destructive operations\n")
	}
	
	builder.WriteString("2. **Explain your actions** clearly before and after command execution\n")
	builder.WriteString("3. **Validate command syntax** and paths before execution\n")
	builder.WriteString("4. **Use safe practices** like --dry-run flags when available\n")
	builder.WriteString("5. **Handle errors gracefully** and provide helpful recovery suggestions\n")
	builder.WriteString("6. **Respect user data** and system integrity at all times\n")
	
	if !sp.config.Terminal.AutoApprove {
		builder.WriteString("7. **Auto-approval is DISABLED** - user must approve each command\n")
	}
	
	return builder.String()
}

// GetToolInstructions returns specific instructions for tool usage
func (sp *SystemPrompt) GetToolInstructions() string {
	return `## Tool Usage Instructions

### Shell Command Tool (execute_shell_command)

**When to Use**:
- File and directory operations (ls, mkdir, cp, mv, rm, etc.)
- System information gathering (ps, top, df, free, etc.)
- Package management (apt, yum, brew, pip, npm, etc.)
- Development tasks (git, build tools, testing, etc.)
- Network operations (ping, curl, wget, etc.)
- Text processing (grep, sed, awk, etc.)

**When NOT to Use**:
- Simple calculations (use your built-in math capabilities)
- Text generation or analysis (use your language model capabilities)
- Theoretical questions (answer directly without system interaction)

**Safety Guidelines**:
1. Always explain what the command will do before executing
2. Use safe flags like --dry-run when available
3. Validate paths and arguments before execution
4. Ask for confirmation for destructive operations
5. Handle errors gracefully and suggest alternatives

**Best Practices**:
- Use appropriate shell syntax for the detected OS
- Capture and analyze command output
- Provide context for command results
- Chain commands logically when needed
- Use parallel execution for independent operations
- Use sequential execution for dependent operations`
}

// UpdateConfig updates the configuration reference
func (sp *SystemPrompt) UpdateConfig(cfg *config.Config) {
	sp.config = cfg
}

// GetPromptStats returns statistics about the system prompt
func (sp *SystemPrompt) GetPromptStats() map[string]interface{} {
	fullPrompt := sp.buildPrompt()
	
	return map[string]interface{}{
		"base_prompt_length":  len(sp.basePrompt),
		"full_prompt_length":  len(fullPrompt),
		"estimated_tokens":    len(strings.Fields(fullPrompt)),
		"provider":           sp.config.Provider.Name,
		"model":              sp.config.Provider.Model,
		"confirm_commands":   sp.config.Terminal.ConfirmCommands,
		"auto_approve":       sp.config.Terminal.AutoApprove,
	}
}
