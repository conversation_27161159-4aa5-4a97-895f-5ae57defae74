package tools

import (
	"context"
	"time"

	"arien-ai/providers"
)

// Tool defines the interface for function calling tools
type Tool interface {
	// GetDefinition returns the tool definition for function calling
	GetDefinition() providers.Tool
	
	// Execute runs the tool with the given arguments
	Execute(ctx context.Context, args string) (*ToolResult, error)
	
	// Name returns the tool name
	Name() string
	
	// Description returns the tool description
	Description() string
}

// ToolResult represents the result of a tool execution
type ToolResult struct {
	Success  bool                   `json:"success"`
	Output   string                 `json:"output"`
	Error    string                 `json:"error,omitempty"`
	Duration time.Duration          `json:"duration"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// ToolManager manages available tools
type ToolManager struct {
	tools map[string]Tool
}

// NewToolManager creates a new tool manager
func NewToolManager() *ToolManager {
	tm := &ToolManager{
		tools: make(map[string]Tool),
	}
	
	// Register built-in tools
	tm.RegisterTool(NewShellTool())
	
	return tm
}

// RegisterTool registers a new tool
func (tm *ToolManager) RegisterTool(tool Tool) {
	tm.tools[tool.Name()] = tool
}

// GetTool returns a tool by name
func (tm *ToolManager) GetTool(name string) (Tool, bool) {
	tool, exists := tm.tools[name]
	return tool, exists
}

// GetAllTools returns all registered tools
func (tm *ToolManager) GetAllTools() map[string]Tool {
	return tm.tools
}

// GetToolDefinitions returns tool definitions for function calling
func (tm *ToolManager) GetToolDefinitions() []providers.Tool {
	definitions := make([]providers.Tool, 0, len(tm.tools))
	for _, tool := range tm.tools {
		definitions = append(definitions, tool.GetDefinition())
	}
	return definitions
}

// ExecuteTool executes a tool by name with the given arguments
func (tm *ToolManager) ExecuteTool(ctx context.Context, name, args string) (*ToolResult, error) {
	tool, exists := tm.GetTool(name)
	if !exists {
		return &ToolResult{
			Success: false,
			Error:   "tool not found: " + name,
		}, nil
	}
	
	return tool.Execute(ctx, args)
}
