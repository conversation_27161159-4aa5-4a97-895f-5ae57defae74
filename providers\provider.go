package providers

import (
	"context"
	"fmt"
)

// Provider defines the interface for LLM providers
type Provider interface {
	// Name returns the provider name
	Name() string
	
	// Models returns available models for this provider
	Models() []string
	
	// Chat sends a chat completion request
	Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error)
	
	// Stream sends a streaming chat completion request
	Stream(ctx context.Context, req *ChatRequest) (<-chan *StreamResponse, error)
	
	// Validate checks if the provider configuration is valid
	Validate() error
}

// ChatRequest represents a chat completion request
type ChatRequest struct {
	Messages    []Message `json:"messages"`
	Model       string    `json:"model"`
	Temperature float64   `json:"temperature,omitempty"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
	Tools       []Tool    `json:"tools,omitempty"`
	ToolChoice  string    `json:"tool_choice,omitempty"`
	Stream      bool      `json:"stream,omitempty"`
}

// ChatResponse represents a chat completion response
type ChatResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

// StreamResponse represents a streaming response chunk
type StreamResponse struct {
	ID      string        `json:"id"`
	Object  string        `json:"object"`
	Created int64         `json:"created"`
	Model   string        `json:"model"`
	Choices []StreamChoice `json:"choices"`
	Done    bool          `json:"done"`
	Error   error         `json:"error,omitempty"`
}

// Message represents a chat message
type Message struct {
	Role       string      `json:"role"`
	Content    string      `json:"content"`
	ToolCalls  []ToolCall  `json:"tool_calls,omitempty"`
	ToolCallID string      `json:"tool_call_id,omitempty"`
}

// Choice represents a completion choice
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

// StreamChoice represents a streaming completion choice
type StreamChoice struct {
	Index        int           `json:"index"`
	Delta        MessageDelta  `json:"delta"`
	FinishReason string        `json:"finish_reason"`
}

// MessageDelta represents a streaming message delta
type MessageDelta struct {
	Role      string     `json:"role,omitempty"`
	Content   string     `json:"content,omitempty"`
	ToolCalls []ToolCall `json:"tool_calls,omitempty"`
}

// Tool represents a function tool
type Tool struct {
	Type     string       `json:"type"`
	Function ToolFunction `json:"function"`
}

// ToolFunction represents a function definition
type ToolFunction struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// ToolCall represents a tool call
type ToolCall struct {
	ID       string           `json:"id"`
	Type     string           `json:"type"`
	Function ToolCallFunction `json:"function"`
}

// ToolCallFunction represents a function call
type ToolCallFunction struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// Usage represents token usage information
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ProviderConfig holds provider configuration
type ProviderConfig struct {
	Name    string
	Model   string
	APIKey  string
	BaseURL string
}

// NewProvider creates a new provider instance
func NewProvider(config ProviderConfig) (Provider, error) {
	switch config.Name {
	case "deepseek":
		return NewDeepseekProvider(config)
	case "ollama":
		return NewOllamaProvider(config)
	default:
		return nil, fmt.Errorf("unsupported provider: %s", config.Name)
	}
}

// GetAvailableProviders returns a list of available providers
func GetAvailableProviders() []string {
	return []string{"deepseek", "ollama"}
}

// GetProviderModels returns available models for a provider
func GetProviderModels(providerName string) []string {
	switch providerName {
	case "deepseek":
		return []string{"deepseek-chat", "deepseek-reasoner"}
	case "ollama":
		return []string{"llama3.2", "llama3.1", "codellama", "mistral", "phi3"}
	default:
		return []string{}
	}
}
