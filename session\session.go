package session

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
)

// Session represents a chat session
type Session struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Messages  []Message `json:"messages"`
	Metadata  Metadata  `json:"metadata"`
}

// Message represents a session message
type Message struct {
	Role      string    `json:"role"`
	Content   string    `json:"content"`
	Timestamp time.Time `json:"timestamp"`
}

// Metadata holds session metadata
type Metadata struct {
	Provider string `json:"provider"`
	Model    string `json:"model"`
	Version  string `json:"version"`
}

// Manager handles session management
type Manager struct {
	sessionsDir string
	current     *Session
}

// NewManager creates a new session manager
func NewManager() (*Manager, error) {
	home, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get user home directory: %w", err)
	}

	sessionsDir := filepath.Join(home, ".arien-ai", "sessions")
	if err := os.MkdirAll(sessionsDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create sessions directory: %w", err)
	}

	return &Manager{
		sessionsDir: sessionsDir,
	}, nil
}

// NewSession creates a new session
func (m *Manager) NewSession(name string, provider string, model string) (*Session, error) {
	if name == "" {
		name = fmt.Sprintf("Session %s", time.Now().Format("2006-01-02 15:04"))
	}

	session := &Session{
		ID:        uuid.New().String(),
		Name:      name,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Messages:  []Message{},
		Metadata: Metadata{
			Provider: provider,
			Model:    model,
			Version:  "1.0.0",
		},
	}

	m.current = session
	return session, m.SaveSession(session)
}

// LoadSession loads a session by ID
func (m *Manager) LoadSession(id string) (*Session, error) {
	sessionFile := filepath.Join(m.sessionsDir, id+".json")
	
	data, err := os.ReadFile(sessionFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read session file: %w", err)
	}

	var session Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to parse session data: %w", err)
	}

	m.current = &session
	return &session, nil
}

// SaveSession saves a session to disk
func (m *Manager) SaveSession(session *Session) error {
	session.UpdatedAt = time.Now()
	
	sessionFile := filepath.Join(m.sessionsDir, session.ID+".json")
	
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session data: %w", err)
	}

	if err := os.WriteFile(sessionFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write session file: %w", err)
	}

	return nil
}

// ListSessions returns all available sessions
func (m *Manager) ListSessions() ([]Session, error) {
	files, err := os.ReadDir(m.sessionsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read sessions directory: %w", err)
	}

	var sessions []Session
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			id := strings.TrimSuffix(file.Name(), ".json")
			session, err := m.LoadSession(id)
			if err != nil {
				continue // Skip corrupted sessions
			}
			sessions = append(sessions, *session)
		}
	}

	// Sort by updated time (newest first)
	sort.Slice(sessions, func(i, j int) bool {
		return sessions[i].UpdatedAt.After(sessions[j].UpdatedAt)
	})

	return sessions, nil
}

// DeleteSession deletes a session
func (m *Manager) DeleteSession(id string) error {
	sessionFile := filepath.Join(m.sessionsDir, id+".json")
	
	if err := os.Remove(sessionFile); err != nil {
		return fmt.Errorf("failed to delete session file: %w", err)
	}

	if m.current != nil && m.current.ID == id {
		m.current = nil
	}

	return nil
}

// GetCurrentSession returns the current session
func (m *Manager) GetCurrentSession() *Session {
	return m.current
}

// AddMessage adds a message to the current session
func (s *Session) AddMessage(message Message) {
	s.Messages = append(s.Messages, message)
	s.UpdatedAt = time.Now()
}

// GetMessages returns all messages in the session
func (s *Session) GetMessages() []Message {
	return s.Messages
}

// ClearMessages clears all messages in the session
func (s *Session) ClearMessages() {
	s.Messages = []Message{}
	s.UpdatedAt = time.Now()
}

// GetSummary returns a summary of the session
func (s *Session) GetSummary() string {
	messageCount := len(s.Messages)
	duration := s.UpdatedAt.Sub(s.CreatedAt)
	
	return fmt.Sprintf("%s (%d messages, %s ago)",
		s.Name,
		messageCount,
		formatDuration(duration))
}

// formatDuration formats a duration in a human-readable way
func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return "just now"
	} else if d < time.Hour {
		return fmt.Sprintf("%d minutes", int(d.Minutes()))
	} else if d < 24*time.Hour {
		return fmt.Sprintf("%d hours", int(d.Hours()))
	} else {
		return fmt.Sprintf("%d days", int(d.Hours()/24))
	}
}
