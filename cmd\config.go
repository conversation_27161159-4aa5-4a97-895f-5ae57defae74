package cmd

import (
	"fmt"
	"os"

	"arien-ai/config"
	"arien-ai/providers"

	"github.com/spf13/cobra"
	"gopkg.in/yaml.v3"
)

// NewConfigCmd creates the config command
func NewConfigCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "config",
		Short: "Manage configuration",
		Long: `Manage Arien AI configuration settings.

Subcommands:
- show: Display current configuration
- set: Set configuration values
- reset: Reset configuration to defaults
- validate: Validate current configuration`,
	}

	cmd.AddCommand(newConfigShowCmd())
	cmd.AddCommand(newConfigSetCmd())
	cmd.AddCommand(newConfigResetCmd())
	cmd.AddCommand(newConfigValidateCmd())

	return cmd
}

func newConfigShowCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "show",
		Short: "Show current configuration",
		RunE: func(cmd *cobra.Command, args []string) error {
			cfg := config.Get()
			
			// Mask sensitive information
			displayCfg := *cfg
			if displayCfg.Provider.APIKey != "" {
				displayCfg.Provider.APIKey = "***masked***"
			}
			
			data, err := yaml.Marshal(&displayCfg)
			if err != nil {
				return fmt.Errorf("failed to marshal config: %w", err)
			}
			
			fmt.Printf("Configuration file: %s\n\n", config.GetConfigFile())
			fmt.Print(string(data))
			
			return nil
		},
	}
	
	return cmd
}

func newConfigSetCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "set",
		Short: "Set configuration values",
		Long: `Set configuration values.

Examples:
  arien-ai config set provider deepseek
  arien-ai config set model deepseek-chat
  arien-ai config set api-key your-api-key
  arien-ai config set base-url https://api.deepseek.com`,
	}

	cmd.AddCommand(&cobra.Command{
		Use:   "provider [name]",
		Short: "Set LLM provider",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			provider := args[0]
			availableProviders := providers.GetAvailableProviders()
			
			// Validate provider
			valid := false
			for _, p := range availableProviders {
				if p == provider {
					valid = true
					break
				}
			}
			
			if !valid {
				return fmt.Errorf("invalid provider: %s. Available: %v", provider, availableProviders)
			}
			
			cfg := config.Get()
			cfg.Provider.Name = provider
			
			// Set default model for provider
			models := providers.GetProviderModels(provider)
			if len(models) > 0 {
				cfg.Provider.Model = models[0]
			}
			
			if err := config.Save(); err != nil {
				return fmt.Errorf("failed to save config: %w", err)
			}
			
			fmt.Printf("Provider set to: %s\n", provider)
			if len(models) > 0 {
				fmt.Printf("Default model set to: %s\n", models[0])
			}
			
			return nil
		},
	})

	cmd.AddCommand(&cobra.Command{
		Use:   "model [name]",
		Short: "Set model",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			model := args[0]
			cfg := config.Get()
			cfg.Provider.Model = model
			
			if err := config.Save(); err != nil {
				return fmt.Errorf("failed to save config: %w", err)
			}
			
			fmt.Printf("Model set to: %s\n", model)
			return nil
		},
	})

	cmd.AddCommand(&cobra.Command{
		Use:   "api-key [key]",
		Short: "Set API key",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			apiKey := args[0]
			cfg := config.Get()
			cfg.Provider.APIKey = apiKey
			
			if err := config.Save(); err != nil {
				return fmt.Errorf("failed to save config: %w", err)
			}
			
			fmt.Println("API key updated")
			return nil
		},
	})

	cmd.AddCommand(&cobra.Command{
		Use:   "base-url [url]",
		Short: "Set base URL",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			baseURL := args[0]
			cfg := config.Get()
			cfg.Provider.BaseURL = baseURL
			
			if err := config.Save(); err != nil {
				return fmt.Errorf("failed to save config: %w", err)
			}
			
			fmt.Printf("Base URL set to: %s\n", baseURL)
			return nil
		},
	})

	return cmd
}

func newConfigResetCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "reset",
		Short: "Reset configuration to defaults",
		RunE: func(cmd *cobra.Command, args []string) error {
			configFile := config.GetConfigFile()
			
			if err := os.Remove(configFile); err != nil && !os.IsNotExist(err) {
				return fmt.Errorf("failed to remove config file: %w", err)
			}
			
			// Reinitialize with defaults
			if err := config.Initialize(); err != nil {
				return fmt.Errorf("failed to reinitialize config: %w", err)
			}
			
			fmt.Println("Configuration reset to defaults")
			return nil
		},
	}
	
	return cmd
}

func newConfigValidateCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "validate",
		Short: "Validate current configuration",
		RunE: func(cmd *cobra.Command, args []string) error {
			cfg := config.Get()
			
			if !config.IsConfigured() {
				fmt.Println("❌ Configuration is incomplete")
				fmt.Println("Run 'arien-ai onboarding' to set up configuration")
				return nil
			}
			
			// Test provider connection
			providerConfig := providers.ProviderConfig{
				Name:    cfg.Provider.Name,
				Model:   cfg.Provider.Model,
				APIKey:  cfg.Provider.APIKey,
				BaseURL: cfg.Provider.BaseURL,
			}
			
			provider, err := providers.NewProvider(providerConfig)
			if err != nil {
				fmt.Printf("❌ Failed to create provider: %v\n", err)
				return nil
			}
			
			if err := provider.Validate(); err != nil {
				fmt.Printf("❌ Provider validation failed: %v\n", err)
				return nil
			}
			
			fmt.Println("✅ Configuration is valid")
			fmt.Printf("Provider: %s\n", cfg.Provider.Name)
			fmt.Printf("Model: %s\n", cfg.Provider.Model)
			fmt.Printf("Base URL: %s\n", cfg.Provider.BaseURL)
			
			return nil
		},
	}
	
	return cmd
}
