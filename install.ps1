# Arien AI Installation Script for Windows
# Supports Windows 10/11, PowerShell 5.1+
# Version: 1.0.0

param(
    [string]$Action = "install",
    [string]$InstallMethod = "binary",
    [string]$Version = "latest",
    [string]$InstallDir = "$env:LOCALAPPDATA\Programs\arien-ai",
    [switch]$Force,
    [switch]$Quiet,
    [switch]$Help
)

# Configuration
$BinaryName = "arien-ai.exe"
$RepoUrl = "https://github.com/your-username/arien-ai"
$GoMinVersion = "1.24"

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Purple = "Magenta"
    Cyan = "Cyan"
}

function Write-Info {
    param([string]$Message)
    if (-not $Quiet) {
        Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
    }
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Write-Step {
    param([string]$Message)
    if (-not $Quiet) {
        Write-Host "[STEP] $Message" -ForegroundColor $Colors.Purple
    }
}

function Write-Banner {
    if (-not $Quiet) {
        Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor $Colors.Cyan
        Write-Host "║                    Arien AI Installation                     ║" -ForegroundColor $Colors.Cyan
        Write-Host "║              Modern CLI Terminal with LLM Integration        ║" -ForegroundColor $Colors.Cyan
        Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor $Colors.Cyan
    }
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Get-Architecture {
    $arch = $env:PROCESSOR_ARCHITECTURE
    switch ($arch) {
        "AMD64" { return "amd64" }
        "ARM64" { return "arm64" }
        default { return "unknown" }
    }
}

function Test-Dependencies {
    $missingDeps = @()
    
    # Check for PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        $missingDeps += "PowerShell 5.1+"
    }
    
    # Check for .NET Framework
    try {
        $dotnetVersion = [System.Runtime.InteropServices.RuntimeInformation]::FrameworkDescription
        Write-Info "Detected .NET: $dotnetVersion"
    } catch {
        $missingDeps += ".NET Framework 4.7.2+"
    }
    
    if ($missingDeps.Count -gt 0) {
        Write-Error "Missing required dependencies: $($missingDeps -join ', ')"
        Write-Info "Please install the missing dependencies and try again."
        exit 1
    }
}

function Test-GoInstallation {
    try {
        $goVersion = & go version 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Go is not installed or not in PATH"
            Write-Info "Please install Go $GoMinVersion+ from https://golang.org/dl/"
            Write-Info "Or use the binary installation method instead"
            return $false
        }
        
        $versionMatch = $goVersion -match "go(\d+\.\d+)"
        if ($versionMatch) {
            $version = [version]$matches[1]
            $minVersion = [version]$GoMinVersion
            
            if ($version -lt $minVersion) {
                Write-Error "Go version $($version) is too old. Minimum required: $GoMinVersion"
                Write-Info "Please upgrade Go or use the binary installation method"
                return $false
            }
            
            Write-Success "Go version $($version) detected"
            return $true
        }
    } catch {
        Write-Error "Failed to check Go installation: $_"
        return $false
    }
    
    return $false
}

function Test-SystemRequirements {
    Write-Step "Checking system requirements..."
    
    $osVersion = [System.Environment]::OSVersion.Version
    Write-Info "Detected OS: Windows $($osVersion.Major).$($osVersion.Minor)"
    
    $arch = Get-Architecture
    Write-Info "Detected Architecture: $arch"
    
    # Check available disk space (at least 100MB)
    $drive = (Get-Location).Drive
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$($drive.Name)'").FreeSpace
    $freeSpaceMB = [math]::Round($freeSpace / 1MB, 2)
    
    if ($freeSpaceMB -lt 100) {
        Write-Warning "Low disk space detected: $freeSpaceMB MB available. At least 100MB recommended."
    }
    
    # Check internet connectivity
    try {
        $null = Test-NetConnection -ComputerName "google.com" -Port 80 -InformationLevel Quiet -WarningAction SilentlyContinue
        if (-not $?) {
            Write-Warning "Internet connectivity check failed. Installation may fail."
        }
    } catch {
        Write-Warning "Could not test internet connectivity."
    }
    
    Write-Success "System requirements check completed"
}

function Install-FromSource {
    Write-Step "Installing Arien AI from source..."
    
    if (-not (Test-GoInstallation)) {
        Write-Error "Go installation check failed"
        exit 1
    }
    
    $tempDir = New-TemporaryFile | ForEach-Object { Remove-Item $_; New-Item -ItemType Directory -Path $_ }
    Write-Info "Using temporary directory: $tempDir"
    
    try {
        Set-Location $tempDir
        
        # Check if we're in a git repository (local development)
        if ((Test-Path "../main.go") -and (Test-Path "../go.mod")) {
            Write-Info "Building from local source..."
            Copy-Item -Path "../*" -Destination "." -Recurse -Force
        } else {
            Write-Info "Cloning repository from $RepoUrl..."
            if (Get-Command git -ErrorAction SilentlyContinue) {
                & git clone $RepoUrl .
                if ($LASTEXITCODE -ne 0) {
                    Write-Error "Failed to clone repository"
                    Write-Info "Please check your internet connection and repository URL"
                    exit 1
                }
            } else {
                Write-Error "Git is not installed. Please install git or use binary installation."
                exit 1
            }
        }
        
        Write-Info "Downloading Go dependencies..."
        & go mod download
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to download Go dependencies"
            exit 1
        }
        
        Write-Info "Running tests..."
        & go test ./...
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "Some tests failed, but continuing with installation..."
        }
        
        Write-Info "Building binary..."
        $ldflags = "-X main.version=$Version"
        
        if (Get-Command git -ErrorAction SilentlyContinue) {
            $commit = & git rev-parse --short HEAD 2>$null
            if ($commit) {
                $ldflags += " -X main.commit=$commit"
            }
        }
        
        $date = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"
        $ldflags += " -X main.date=$date"
        
        $arch = Get-Architecture
        Write-Info "Building for windows/$arch..."
        
        $env:GOOS = "windows"
        $env:GOARCH = $arch
        & go build -ldflags $ldflags -o $BinaryName
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to build binary"
            exit 1
        }
        
        Write-Info "Installing binary to $InstallDir..."
        Install-BinaryToSystem (Join-Path $tempDir $BinaryName)
        
        Write-Success "Arien AI installed successfully from source!"
    } finally {
        Set-Location $env:USERPROFILE
        Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

function Install-BinaryToSystem {
    param([string]$BinaryPath)
    
    # Create install directory if it doesn't exist
    if (-not (Test-Path $InstallDir)) {
        Write-Info "Creating install directory: $InstallDir"
        New-Item -ItemType Directory -Path $InstallDir -Force | Out-Null
    }
    
    # Copy binary
    $targetPath = Join-Path $InstallDir $BinaryName
    Copy-Item -Path $BinaryPath -Destination $targetPath -Force
    
    # Verify installation
    if (-not (Test-Path $targetPath)) {
        Write-Error "Binary installation verification failed"
        exit 1
    }
    
    Write-Success "Binary installed to $targetPath"
}

function Install-FromBinary {
    $arch = Get-Architecture
    
    if ($arch -eq "unknown") {
        Write-Error "Unsupported architecture: $env:PROCESSOR_ARCHITECTURE"
        Write-Info "Falling back to source installation..."
        Install-FromSource
        return
    }
    
    Write-Info "Installing Arien AI binary for windows-$arch..."
    
    $downloadUrl = "$RepoUrl/releases/latest/download/$($BinaryName.Replace('.exe', ''))-windows-$arch.exe"
    $tempFile = New-TemporaryFile
    
    try {
        Write-Info "Downloading from $downloadUrl..."
        Invoke-WebRequest -Uri $downloadUrl -OutFile $tempFile -UseBasicParsing
        
        if ((Get-Item $tempFile).Length -eq 0) {
            Write-Warning "Binary download failed, falling back to source installation..."
            Install-FromSource
            return
        }
        
        Write-Info "Installing binary to $InstallDir..."
        Install-BinaryToSystem $tempFile
        
        Write-Success "Arien AI installed successfully!"
    } catch {
        Write-Warning "Binary download failed: $_"
        Write-Info "Falling back to source installation..."
        Install-FromSource
    } finally {
        Remove-Item -Path $tempFile -Force -ErrorAction SilentlyContinue
    }
}

function Update-Installation {
    Write-Info "Updating existing Arien AI installation..."

    $targetPath = Join-Path $InstallDir $BinaryName
    if (-not (Test-Path $targetPath)) {
        Write-Error "Arien AI is not installed in $InstallDir"
        exit 1
    }

    # Backup current version
    $backupPath = "$targetPath.backup"
    Copy-Item -Path $targetPath -Destination $backupPath -Force
    Write-Info "Backed up current version to $backupPath"

    try {
        # Install new version
        if ($InstallMethod -eq "source") {
            Install-FromSource
        } else {
            Install-FromBinary
        }

        # Test new installation
        $version = & $targetPath --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Update completed successfully!"
            Remove-Item -Path $backupPath -Force -ErrorAction SilentlyContinue
        } else {
            throw "Installation verification failed"
        }
    } catch {
        Write-Error "Update failed, restoring backup: $_"
        Copy-Item -Path $backupPath -Destination $targetPath -Force
        Remove-Item -Path $backupPath -Force -ErrorAction SilentlyContinue
        exit 1
    }
}

function Uninstall-ArienAI {
    Write-Info "Uninstalling Arien AI..."

    $targetPath = Join-Path $InstallDir $BinaryName
    if (Test-Path $targetPath) {
        Remove-Item -Path $targetPath -Force
        Write-Success "Binary removed from $InstallDir"
    } else {
        Write-Warning "Binary not found in $InstallDir"
    }

    # Remove from PATH if present
    $userPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($userPath -like "*$InstallDir*") {
        $newPath = ($userPath -split ';' | Where-Object { $_ -ne $InstallDir }) -join ';'
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Success "Removed from user PATH"
    }

    # Remove configuration (optional)
    $configDir = Join-Path $env:USERPROFILE ".arien-ai"
    if (Test-Path $configDir) {
        $response = Read-Host "Remove configuration directory $configDir? (y/N)"
        if ($response -eq 'y' -or $response -eq 'Y') {
            Remove-Item -Path $configDir -Recurse -Force
            Write-Success "Configuration directory removed"
        }
    }

    Write-Success "Arien AI uninstalled successfully!"
}

function Show-Usage {
    Write-Banner
    Write-Host ""
    Write-Host "Usage: .\install.ps1 [ACTION] [OPTIONS]"
    Write-Host ""
    Write-Host "Actions:"
    Write-Host "  install          Install Arien AI (default)"
    Write-Host "  update           Update existing installation"
    Write-Host "  uninstall        Remove Arien AI completely"
    Write-Host "  check            Check system requirements"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -InstallMethod   'source' or 'binary' (default: binary)"
    Write-Host "  -Version         Install specific version (default: latest)"
    Write-Host "  -InstallDir      Install to custom directory"
    Write-Host "  -Force           Force installation even if already installed"
    Write-Host "  -Quiet           Suppress non-essential output"
    Write-Host "  -Help            Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\install.ps1                                    # Install latest binary"
    Write-Host "  .\install.ps1 -InstallMethod source             # Install from source"
    Write-Host "  .\install.ps1 update                           # Update existing installation"
    Write-Host "  .\install.ps1 uninstall                        # Remove installation"
    Write-Host "  .\install.ps1 check                            # Check system requirements"
    Write-Host ""
    Write-Host "For more information, visit: $RepoUrl"
}

function Test-ExistingInstallation {
    $targetPath = Join-Path $InstallDir $BinaryName
    if (Test-Path $targetPath) {
        try {
            $currentVersion = & $targetPath --version 2>$null | Select-Object -First 1
            if ($currentVersion) {
                Write-Info "Existing installation found: $currentVersion"
                return $true
            }
        } catch {
            # Ignore errors
        }
    }
    return $false
}

function Set-PathEnvironment {
    Write-Step "Post-installation setup..."

    # Check if binary is in PATH
    $targetPath = Join-Path $InstallDir $BinaryName
    try {
        $null = Get-Command $BinaryName -ErrorAction Stop
        Write-Success "$BinaryName is already in PATH"
    } catch {
        Write-Warning "$BinaryName is not in your PATH"
        Write-Info "Adding $InstallDir to your PATH..."

        # Add to user PATH
        $userPath = [Environment]::GetEnvironmentVariable("PATH", "User")
        if ($userPath -notlike "*$InstallDir*") {
            $newPath = if ($userPath) { "$userPath;$InstallDir" } else { $InstallDir }
            [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
            Write-Success "Added $InstallDir to user PATH"
            Write-Info "Please restart your PowerShell session or run: `$env:PATH += ';$InstallDir'"
        }
    }

    # Test installation
    try {
        $version = & $targetPath --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Installation verified successfully!"
        } else {
            Write-Warning "Installation verification failed"
        }
    } catch {
        Write-Warning "Installation verification failed: $_"
    }
}

# Main script execution
if ($Help) {
    Show-Usage
    exit 0
}

# Show banner unless quiet
if (-not $Quiet) {
    Write-Banner
    Write-Host ""
    Write-Info "Action: $Action"
    Write-Info "Method: $InstallMethod"
    Write-Info "Version: $Version"
    Write-Info "Install Directory: $InstallDir"
    Write-Host ""
}

switch ($Action.ToLower()) {
    "check" {
        Test-SystemRequirements
        Test-Dependencies
        if ($InstallMethod -eq "source") {
            Test-GoInstallation
        }
        Write-Success "System check completed"
        exit 0
    }
    "install" {
        # Check for existing installation
        if ((Test-ExistingInstallation) -and (-not $Force)) {
            Write-Warning "Arien AI is already installed"
            Write-Info "Use 'update' action to update or -Force to reinstall"
            exit 0
        }

        Test-SystemRequirements
        Test-Dependencies

        if ($InstallMethod -eq "source") {
            Install-FromSource
        } else {
            Install-FromBinary
        }

        Set-PathEnvironment
    }
    "update" {
        Update-Installation
    }
    "uninstall" {
        Uninstall-ArienAI
        exit 0
    }
    default {
        Write-Error "Unknown action: $Action"
        Show-Usage
        exit 1
    }
}

# Show next steps
if ($Action -ne "uninstall" -and (-not $Quiet)) {
    Write-Host ""
    Write-Step "Next steps:"
    Write-Host "1. Run 'arien-ai onboarding' to set up your configuration"
    Write-Host "2. Run 'arien-ai interactive' to start the AI terminal"
    Write-Host "3. Use 'arien-ai --help' to see all available commands"
    Write-Host ""
    Write-Info "For support and documentation, visit: $RepoUrl"
}
