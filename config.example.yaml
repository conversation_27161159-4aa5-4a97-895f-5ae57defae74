# Arien AI Configuration Example
# Copy this file to ~/.arien-ai.yaml and customize as needed

# LLM Provider Configuration
provider:
  # Provider name: deepseek or ollama
  name: deepseek
  
  # Model to use
  model: deepseek-chat
  
  # API key (required for deepseek, optional for ollama)
  api_key: your-api-key-here
  
  # Base URL for the provider
  base_url: https://api.deepseek.com
  
  # Additional provider-specific settings
  settings:
    temperature: "0.7"
    max_tokens: "4096"

# Session Management
session:
  # Save chat history to disk
  save_history: true
  
  # Custom history file location (optional)
  # history_file: ~/.arien-ai/history.json
  
  # Maximum number of messages to keep in memory
  max_history: 1000
  
  # Session timeout in seconds (0 = no timeout)
  session_timeout: 3600

# Terminal UI Configuration
terminal:
  # UI theme (default, dark, light)
  theme: default
  
  # Show timestamps in chat messages
  show_timestamps: true
  
  # Automatically approve safe commands (not recommended)
  auto_approve: false
  
  # Confirm before executing potentially dangerous commands
  confirm_commands: true

# Logging Configuration
logging:
  # Log level: debug, info, warn, error
  level: info
  
  # Log file location (optional)
  file: ~/.arien-ai/logs/arien-ai.log
  
  # Also log to console
  console: true
  
  # Log format: text or json
  format: text

# Retry Policy for Network Operations
retry:
  # Maximum number of retries for failed requests
  max_retries: 3
  
  # Initial delay between retries (milliseconds)
  initial_delay: 1000
  
  # Maximum delay between retries (milliseconds)
  max_delay: 30000
  
  # Backoff factor for exponential backoff
  backoff_factor: 2
  
  # Network timeout in seconds
  network_timeout: 30

# Example configurations for different providers:

# Deepseek Configuration
# provider:
#   name: deepseek
#   model: deepseek-chat  # or deepseek-reasoner
#   api_key: your-deepseek-api-key
#   base_url: https://api.deepseek.com

# Ollama Configuration
# provider:
#   name: ollama
#   model: llama3.2  # or any installed model
#   base_url: http://localhost:11434
#   # api_key not required for local ollama

# Advanced Terminal Settings
# terminal:
#   theme: dark
#   show_timestamps: true
#   auto_approve: false
#   confirm_commands: true
#   # Custom prompt format
#   prompt_format: "[{timestamp}] {role}: {content}"
#   # Maximum terminal width
#   max_width: 120

# Advanced Logging Settings
# logging:
#   level: debug
#   file: /var/log/arien-ai/app.log
#   console: false
#   format: json
#   # Rotate logs
#   max_size: 100  # MB
#   max_backups: 5
#   max_age: 30    # days

# Custom Retry Policy
# retry:
#   max_retries: 5
#   initial_delay: 500
#   max_delay: 60000
#   backoff_factor: 1.5
#   network_timeout: 60
#   # Retry specific error codes
#   retry_codes: [429, 500, 502, 503, 504]
