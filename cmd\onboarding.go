package cmd

import (
	"fmt"
	"strings"

	"arien-ai/config"
	"arien-ai/providers"

	"github.com/manifoldco/promptui"
	"github.com/spf13/cobra"
)

// NewOnboardingCmd creates the onboarding command
func NewOnboardingCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "onboarding",
		Short: "Setup Arien AI configuration",
		Long: `Interactive onboarding process to configure Arien AI.

This will guide you through:
- Selecting an LLM provider (Deepseek or Ollama)
- Configuring API keys and endpoints
- Choosing default models
- Setting up preferences

After completion, you can start using the interactive terminal.`,
		Aliases: []string{"setup", "init", "configure"},
		RunE:    runOnboarding,
	}

	cmd.Flags().BoolP("reset", "r", false, "Reset existing configuration")
	cmd.Flags().BoolP("auto-start", "a", false, "Automatically start interactive mode after setup")

	return cmd
}

func runOnboarding(cmd *cobra.Command, args []string) error {
	reset, _ := cmd.Flags().GetBool("reset")
	autoStart, _ := cmd.Flags().GetBool("auto-start")

	fmt.Println("🚀 Welcome to Arien AI Setup!")
	fmt.Println("Let's configure your AI-powered terminal.")
	fmt.Println()

	// Check existing configuration
	if config.IsConfigured() && !reset {
		fmt.Println("✅ Configuration already exists.")
		
		prompt := promptui.Prompt{
			Label:     "Do you want to reconfigure",
			IsConfirm: true,
		}
		
		result, err := prompt.Run()
		if err != nil || strings.ToLower(result) != "y" {
			fmt.Println("Configuration unchanged. Use --reset to force reconfiguration.")
			return nil
		}
	}

	// Step 1: Select Provider
	providerPrompt := promptui.Select{
		Label: "Select LLM Provider",
		Items: []string{
			"deepseek - Deepseek AI (requires API key)",
			"ollama - Local Ollama instance",
		},
	}

	providerIndex, _, err := providerPrompt.Run()
	if err != nil {
		return fmt.Errorf("provider selection failed: %w", err)
	}

	var providerName string
	var baseURL string
	var apiKey string

	switch providerIndex {
	case 0: // Deepseek
		providerName = "deepseek"
		baseURL = "https://api.deepseek.com"
		
		// Get API key
		apiKeyPrompt := promptui.Prompt{
			Label: "Enter your Deepseek API key",
			Mask:  '*',
			Validate: func(input string) error {
				if strings.TrimSpace(input) == "" {
					return fmt.Errorf("API key cannot be empty")
				}
				return nil
			},
		}
		
		apiKey, err = apiKeyPrompt.Run()
		if err != nil {
			return fmt.Errorf("API key input failed: %w", err)
		}

	case 1: // Ollama
		providerName = "ollama"
		
		// Get Ollama URL
		urlPrompt := promptui.Prompt{
			Label:   "Enter Ollama URL",
			Default: "http://localhost:11434",
		}
		
		baseURL, err = urlPrompt.Run()
		if err != nil {
			return fmt.Errorf("URL input failed: %w", err)
		}
	}

	// Step 2: Select Model
	availableModels := providers.GetProviderModels(providerName)
	if len(availableModels) == 0 {
		return fmt.Errorf("no models available for provider: %s", providerName)
	}

	modelPrompt := promptui.Select{
		Label: "Select Model",
		Items: availableModels,
	}

	_, model, err := modelPrompt.Run()
	if err != nil {
		return fmt.Errorf("model selection failed: %w", err)
	}

	// Step 3: Test Configuration
	fmt.Println("\n🔧 Testing configuration...")

	providerConfig := providers.ProviderConfig{
		Name:    providerName,
		Model:   model,
		APIKey:  apiKey,
		BaseURL: baseURL,
	}

	provider, err := providers.NewProvider(providerConfig)
	if err != nil {
		return fmt.Errorf("failed to create provider: %w", err)
	}

	if err := provider.Validate(); err != nil {
		fmt.Printf("❌ Configuration test failed: %v\n", err)
		fmt.Println("Please check your settings and try again.")
		return nil
	}

	fmt.Println("✅ Configuration test successful!")

	// Step 4: Save Configuration
	if err := config.SetProvider(providerName, model, apiKey, baseURL); err != nil {
		return fmt.Errorf("failed to save configuration: %w", err)
	}

	fmt.Println("✅ Configuration saved successfully!")
	fmt.Printf("Provider: %s\n", providerName)
	fmt.Printf("Model: %s\n", model)
	fmt.Printf("Base URL: %s\n", baseURL)

	// Step 5: Additional Settings
	fmt.Println("\n⚙️  Additional Settings")

	confirmPrompt := promptui.Prompt{
		Label:     "Enable command confirmation prompts",
		IsConfirm: true,
		Default:   "y",
	}

	confirmResult, _ := confirmPrompt.Run()
	confirmCommands := strings.ToLower(confirmResult) == "y"

	timestampPrompt := promptui.Prompt{
		Label:     "Show timestamps in chat",
		IsConfirm: true,
		Default:   "y",
	}

	timestampResult, _ := timestampPrompt.Run()
	showTimestamps := strings.ToLower(timestampResult) == "y"

	// Update additional settings
	cfg := config.Get()
	cfg.Terminal.ConfirmCommands = confirmCommands
	cfg.Terminal.ShowTimestamps = showTimestamps

	if err := config.Save(); err != nil {
		fmt.Printf("⚠️  Warning: Failed to save additional settings: %v\n", err)
	}

	fmt.Println("\n🎉 Setup complete!")
	fmt.Println("You can now use 'arien-ai interactive' to start the terminal.")

	// Auto-start if requested
	if autoStart {
		fmt.Println("\n🚀 Starting interactive mode...")
		interactiveCmd := NewInteractiveCmd()
		return interactiveCmd.RunE(interactiveCmd, []string{})
	}

	return nil
}
