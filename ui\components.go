package ui

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"arien-ai/config"
	"arien-ai/providers"
	"arien-ai/session"
)

// HeaderComponent renders the terminal header
type HeaderComponent struct {
	styles Styles
}

// NewHeaderComponent creates a new header component
func NewHeaderComponent(styles Styles) *HeaderComponent {
	return &HeaderComponent{
		styles: styles,
	}
}

// <PERSON>der renders the header with current session info
func (h *HeaderComponent) Render(sess *session.Session, width int) string {
	cfg := config.Get()
	
	// Get current working directory
	cwd, err := os.Getwd()
	if err != nil {
		cwd = "unknown"
	} else {
		// Show only the last 2 directories for brevity
		parts := strings.Split(filepath.Clean(cwd), string(filepath.Separator))
		if len(parts) > 2 {
			cwd = ".../" + strings.Join(parts[len(parts)-2:], "/")
		}
	}

	// Create header sections
	leftSection := fmt.Sprintf("🤖 Arien AI Terminal")
	
	centerSection := fmt.Sprintf("Provider: %s | Model: %s", 
		cfg.Provider.Name, cfg.Provider.Model)
	
	rightSection := fmt.Sprintf("Session: %s | Dir: %s", 
		sess.ID[:8], cwd)

	// Calculate spacing
	totalContentWidth := len(leftSection) + len(centerSection) + len(rightSection)
	if totalContentWidth >= width-4 {
		// Truncate if too long
		centerSection = fmt.Sprintf("%s|%s", cfg.Provider.Name, cfg.Provider.Model)
		rightSection = fmt.Sprintf("%s|%s", sess.ID[:6], filepath.Base(cwd))
	}

	// Create header line
	padding := width - len(leftSection) - len(centerSection) - len(rightSection)

	// Ensure padding is not negative
	if padding < 0 {
		padding = 0
	}

	leftPadding := padding / 3
	rightPadding := padding - leftPadding

	// Ensure padding values are not negative
	if leftPadding < 0 {
		leftPadding = 0
	}
	if rightPadding < 0 {
		rightPadding = 0
	}

	headerLine := leftSection +
		strings.Repeat(" ", leftPadding) +
		centerSection +
		strings.Repeat(" ", rightPadding) +
		rightSection

	return h.styles.Header.Width(width).Render(headerLine)
}

// ThinkingComponent handles the thinking animation
type ThinkingComponent struct {
	styles     Styles
	ballFrames []string
	frame      int
	startTime  time.Time
}

// NewThinkingComponent creates a new thinking component
func NewThinkingComponent(styles Styles) *ThinkingComponent {
	ballFrames := []string{
		"( ●    )",
		"(  ●   )",
		"(   ●  )",
		"(    ● )",
		"(     ●)",
		"(    ● )",
		"(   ●  )",
		"(  ●   )",
		"( ●    )",
		"(●     )",
	}

	return &ThinkingComponent{
		styles:     styles,
		ballFrames: ballFrames,
		frame:      0,
		startTime:  time.Now(),
	}
}

// NextFrame advances to the next animation frame
func (t *ThinkingComponent) NextFrame() {
	t.frame = (t.frame + 1) % len(t.ballFrames)
}

// Render renders the thinking animation with elapsed time
func (t *ThinkingComponent) Render(message string) string {
	elapsed := time.Since(t.startTime)
	elapsedSeconds := int(elapsed.Seconds())
	
	var timeDisplay string
	if elapsedSeconds < 60 {
		timeDisplay = fmt.Sprintf("%ds", elapsedSeconds)
	} else {
		minutes := elapsedSeconds / 60
		seconds := elapsedSeconds % 60
		timeDisplay = fmt.Sprintf("%dm%ds", minutes, seconds)
	}

	thinkingText := fmt.Sprintf("%s %s (%s)", 
		t.ballFrames[t.frame], message, timeDisplay)
	
	return t.styles.ThinkingMsg.Render(thinkingText)
}

// Reset resets the thinking animation
func (t *ThinkingComponent) Reset() {
	t.frame = 0
	t.startTime = time.Now()
}

// SlashCommandsComponent handles slash command processing
type SlashCommandsComponent struct {
	styles Styles
}

// NewSlashCommandsComponent creates a new slash commands component
func NewSlashCommandsComponent(styles Styles) *SlashCommandsComponent {
	return &SlashCommandsComponent{
		styles: styles,
	}
}

// GetHelpText returns the help text for slash commands
func (s *SlashCommandsComponent) GetHelpText() string {
	return `Available slash commands:
/help - Show this help message
/model [model_name] - Change the current model
/provider [provider_name] - Change the current provider  
/session new - Create a new session
/session list - List all sessions
/session load <id> - Load a specific session
/session delete <id> - Delete a session
/message-history - Show message history
/clear - Clear message history
/config - Show current configuration
/quit - Exit the application

Examples:
/model deepseek-chat
/provider ollama
/session new
/session load abc12345`
}

// ProcessCommand processes a slash command and returns the result
func (s *SlashCommandsComponent) ProcessCommand(command string, sess *session.Session) (string, error) {
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return "", fmt.Errorf("empty command")
	}

	cmd := parts[0]
	args := parts[1:]

	switch cmd {
	case "/help":
		return s.GetHelpText(), nil

	case "/model":
		if len(args) == 0 {
			cfg := config.Get()
			return fmt.Sprintf("Current model: %s", cfg.Provider.Model), nil
		}
		return s.handleModelSwitch(args[0])

	case "/provider":
		if len(args) == 0 {
			cfg := config.Get()
			return fmt.Sprintf("Current provider: %s", cfg.Provider.Name), nil
		}
		return s.handleProviderSwitch(args[0])

	case "/session":
		return s.handleSessionCommand(args, sess)

	case "/message-history":
		return s.formatMessageHistory(sess), nil

	case "/clear":
		sess.ClearMessages()
		return "Message history cleared", nil

	case "/config":
		return s.formatConfig(), nil

	case "/quit":
		return "Goodbye!", nil

	default:
		return "", fmt.Errorf("unknown command: %s. Type /help for available commands", cmd)
	}
}

// handleModelSwitch handles model switching
func (s *SlashCommandsComponent) handleModelSwitch(model string) (string, error) {
	cfg := config.Get()

	// Validate model for current provider
	availableModels := providers.GetProviderModels(cfg.Provider.Name)
	valid := false
	for _, m := range availableModels {
		if m == model {
			valid = true
			break
		}
	}

	if !valid {
		return "", fmt.Errorf("invalid model '%s' for provider '%s'. Available models: %v",
			model, cfg.Provider.Name, availableModels)
	}

	// Update configuration
	cfg.Provider.Model = model
	if err := config.Save(); err != nil {
		return "", fmt.Errorf("failed to save configuration: %w", err)
	}

	return fmt.Sprintf("✅ Model switched to: %s\nNote: Restart interactive mode to use the new model", model), nil
}

// handleProviderSwitch handles provider switching
func (s *SlashCommandsComponent) handleProviderSwitch(provider string) (string, error) {
	availableProviders := providers.GetAvailableProviders()
	valid := false
	for _, p := range availableProviders {
		if p == provider {
			valid = true
			break
		}
	}

	if !valid {
		return "", fmt.Errorf("invalid provider '%s'. Available providers: %v",
			provider, availableProviders)
	}

	cfg := config.Get()
	cfg.Provider.Name = provider

	// Set default model for new provider
	models := providers.GetProviderModels(provider)
	if len(models) > 0 {
		cfg.Provider.Model = models[0]
	}

	if err := config.Save(); err != nil {
		return "", fmt.Errorf("failed to save configuration: %w", err)
	}

	result := fmt.Sprintf("✅ Provider switched to: %s", provider)
	if len(models) > 0 {
		result += fmt.Sprintf("\n✅ Default model set to: %s", models[0])
	}
	result += "\nNote: Restart interactive mode to use the new provider"

	return result, nil
}

// handleSessionCommand handles session-related commands
func (s *SlashCommandsComponent) handleSessionCommand(args []string, sess *session.Session) (string, error) {
	if len(args) == 0 {
		return fmt.Sprintf("Current session: %s (%s)", sess.ID, sess.Name), nil
	}

	switch args[0] {
	case "new":
		return s.handleNewSession()
	case "list":
		return s.handleListSessions()
	case "load":
		if len(args) < 2 {
			return "", fmt.Errorf("session load requires a session ID")
		}
		return s.handleLoadSession(args[1])
	case "delete":
		if len(args) < 2 {
			return "", fmt.Errorf("session delete requires a session ID")
		}
		return s.handleDeleteSession(args[1])
	default:
		return "", fmt.Errorf("unknown session command: %s", args[0])
	}
}

// handleNewSession handles creating a new session
func (s *SlashCommandsComponent) handleNewSession() (string, error) {
	manager, err := session.NewManager()
	if err != nil {
		return "", fmt.Errorf("failed to initialize session manager: %w", err)
	}

	cfg := config.Get()
	newSess, err := manager.NewSession("", cfg.Provider.Name, cfg.Provider.Model)
	if err != nil {
		return "", fmt.Errorf("failed to create new session: %w", err)
	}

	return fmt.Sprintf("✅ Created new session: %s\nNote: Use '/session load %s' to switch to it",
		newSess.ID, newSess.ID), nil
}

// handleListSessions handles listing all sessions
func (s *SlashCommandsComponent) handleListSessions() (string, error) {
	manager, err := session.NewManager()
	if err != nil {
		return "", fmt.Errorf("failed to initialize session manager: %w", err)
	}

	sessions, err := manager.ListSessions()
	if err != nil {
		return "", fmt.Errorf("failed to list sessions: %w", err)
	}

	if len(sessions) == 0 {
		return "No sessions found", nil
	}

	var result strings.Builder
	result.WriteString(fmt.Sprintf("Available sessions (%d):\n", len(sessions)))

	for i, sess := range sessions {
		result.WriteString(fmt.Sprintf("%d. %s - %s (%d messages, updated %s)\n",
			i+1, sess.ID[:8], sess.Name, len(sess.Messages),
			sess.UpdatedAt.Format("2006-01-02 15:04")))
	}

	return result.String(), nil
}

// handleLoadSession handles loading a specific session
func (s *SlashCommandsComponent) handleLoadSession(sessionID string) (string, error) {
	return fmt.Sprintf("Session loading requires application restart. Use: arien-ai interactive --session %s",
		sessionID), nil
}

// handleDeleteSession handles deleting a session
func (s *SlashCommandsComponent) handleDeleteSession(sessionID string) (string, error) {
	manager, err := session.NewManager()
	if err != nil {
		return "", fmt.Errorf("failed to initialize session manager: %w", err)
	}

	if err := manager.DeleteSession(sessionID); err != nil {
		return "", fmt.Errorf("failed to delete session: %w", err)
	}

	return fmt.Sprintf("✅ Session %s deleted successfully", sessionID), nil
}

// formatMessageHistory formats the message history for display
func (s *SlashCommandsComponent) formatMessageHistory(sess *session.Session) string {
	messages := sess.GetMessages()
	if len(messages) == 0 {
		return "No messages in current session"
	}

	var result strings.Builder
	result.WriteString(fmt.Sprintf("Message History (%d messages):\n", len(messages)))
	
	for i, msg := range messages {
		timestamp := msg.Timestamp.Format("15:04:05")
		result.WriteString(fmt.Sprintf("%d. [%s] %s: %s\n", 
			i+1, timestamp, strings.Title(msg.Role), 
			truncateString(msg.Content, 100)))
	}

	return result.String()
}

// formatConfig formats the current configuration for display
func (s *SlashCommandsComponent) formatConfig() string {
	cfg := config.Get()
	
	return fmt.Sprintf(`Current Configuration:
Provider: %s
Model: %s
Base URL: %s
Auto Approve: %t
Confirm Commands: %t
Show Timestamps: %t
Max History: %d
Session Timeout: %d seconds`,
		cfg.Provider.Name,
		cfg.Provider.Model,
		cfg.Provider.BaseURL,
		cfg.Terminal.AutoApprove,
		cfg.Terminal.ConfirmCommands,
		cfg.Terminal.ShowTimestamps,
		cfg.Session.MaxHistory,
		cfg.Session.SessionTimeout)
}

// truncateString truncates a string to the specified length
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}
