package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Provider    ProviderConfig `mapstructure:"provider" yaml:"provider"`
	Session     SessionConfig  `mapstructure:"session" yaml:"session"`
	Terminal    TerminalConfig `mapstructure:"terminal" yaml:"terminal"`
	Logging     LoggingConfig  `mapstructure:"logging" yaml:"logging"`
	RetryPolicy RetryConfig    `mapstructure:"retry" yaml:"retry"`
}

// ProviderConfig holds LLM provider configuration
type ProviderConfig struct {
	Name     string            `mapstructure:"name" yaml:"name"`
	Model    string            `mapstructure:"model" yaml:"model"`
	APIKey   string            `mapstructure:"api_key" yaml:"api_key"`
	BaseURL  string            `mapstructure:"base_url" yaml:"base_url"`
	Settings map[string]string `mapstructure:"settings" yaml:"settings"`
}

// SessionConfig holds session management configuration
type SessionConfig struct {
	SaveHistory    bool   `mapstructure:"save_history" yaml:"save_history"`
	HistoryFile    string `mapstructure:"history_file" yaml:"history_file"`
	MaxHistory     int    `mapstructure:"max_history" yaml:"max_history"`
	SessionTimeout int    `mapstructure:"session_timeout" yaml:"session_timeout"`
}

// TerminalConfig holds terminal UI configuration
type TerminalConfig struct {
	Theme           string `mapstructure:"theme" yaml:"theme"`
	ShowTimestamps  bool   `mapstructure:"show_timestamps" yaml:"show_timestamps"`
	AutoApprove     bool   `mapstructure:"auto_approve" yaml:"auto_approve"`
	ConfirmCommands bool   `mapstructure:"confirm_commands" yaml:"confirm_commands"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level    string `mapstructure:"level" yaml:"level"`
	File     string `mapstructure:"file" yaml:"file"`
	Console  bool   `mapstructure:"console" yaml:"console"`
	Format   string `mapstructure:"format" yaml:"format"`
}

// RetryConfig holds retry policy configuration
type RetryConfig struct {
	MaxRetries      int `mapstructure:"max_retries" yaml:"max_retries"`
	InitialDelay    int `mapstructure:"initial_delay" yaml:"initial_delay"`
	MaxDelay        int `mapstructure:"max_delay" yaml:"max_delay"`
	BackoffFactor   int `mapstructure:"backoff_factor" yaml:"backoff_factor"`
	NetworkTimeout  int `mapstructure:"network_timeout" yaml:"network_timeout"`
}

var (
	cfg        *Config
	configFile string
)

// Initialize sets up the configuration system
func Initialize() error {
	// Set config file path
	home, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("failed to get user home directory: %w", err)
	}

	configFile = filepath.Join(home, ".arien-ai.yaml")
	
	// Set up viper
	viper.SetConfigName(".arien-ai")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(home)
	viper.AddConfigPath(".")

	// Set defaults
	setDefaults()

	// Read config file if it exists
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// Unmarshal config
	cfg = &Config{}
	if err := viper.Unmarshal(cfg); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Provider defaults
	viper.SetDefault("provider.name", "deepseek")
	viper.SetDefault("provider.model", "deepseek-chat")
	viper.SetDefault("provider.base_url", "https://api.deepseek.com")

	// Session defaults
	viper.SetDefault("session.save_history", true)
	viper.SetDefault("session.max_history", 1000)
	viper.SetDefault("session.session_timeout", 3600)

	// Terminal defaults
	viper.SetDefault("terminal.theme", "default")
	viper.SetDefault("terminal.show_timestamps", true)
	viper.SetDefault("terminal.auto_approve", false)
	viper.SetDefault("terminal.confirm_commands", true)

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.console", true)
	viper.SetDefault("logging.format", "text")

	// Retry defaults
	viper.SetDefault("retry.max_retries", 3)
	viper.SetDefault("retry.initial_delay", 1000)
	viper.SetDefault("retry.max_delay", 30000)
	viper.SetDefault("retry.backoff_factor", 2)
	viper.SetDefault("retry.network_timeout", 30)
}

// Get returns the current configuration
func Get() *Config {
	return cfg
}

// Save writes the current configuration to file
func Save() error {
	return viper.WriteConfigAs(configFile)
}

// SetProvider updates the provider configuration
func SetProvider(name, model, apiKey, baseURL string) error {
	viper.Set("provider.name", name)
	viper.Set("provider.model", model)
	viper.Set("provider.api_key", apiKey)
	if baseURL != "" {
		viper.Set("provider.base_url", baseURL)
	}

	// Update in-memory config
	cfg.Provider.Name = name
	cfg.Provider.Model = model
	cfg.Provider.APIKey = apiKey
	if baseURL != "" {
		cfg.Provider.BaseURL = baseURL
	}

	return Save()
}

// GetConfigFile returns the path to the config file
func GetConfigFile() string {
	return configFile
}

// IsConfigured checks if the basic configuration is set
func IsConfigured() bool {
	return cfg.Provider.Name != "" && cfg.Provider.APIKey != ""
}
