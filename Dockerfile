# Multi-stage build for Arien AI
FROM golang:1.24-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the binary
ARG VERSION=1.0.0
ARG COMMIT=docker
ARG DATE
RUN if [ -z "$DATE" ]; then DATE=$(date -u +%Y-%m-%dT%H:%M:%SZ); fi && \
    CGO_ENABLED=0 GOOS=linux go build \
    -ldflags "-X main.version=$VERSION -X main.commit=$COMMIT -X main.date=$DATE -w -s" \
    -o arien-ai .

# Final stage
FROM alpine:latest

# Install runtime dependencies
RUN apk add --no-cache \
    ca-certificates \
    curl \
    git \
    bash \
    openssh-client \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S arien && \
    adduser -u 1001 -S arien -G arien

# Set working directory
WORKDIR /home/<USER>

# Copy binary from builder
COPY --from=builder /app/arien-ai /usr/local/bin/arien-ai

# Copy system prompt
COPY --from=builder /app/prompts/system.md /etc/arien-ai/system.md

# Set permissions
RUN chmod +x /usr/local/bin/arien-ai

# Switch to non-root user
USER arien

# Create config directory
RUN mkdir -p /home/<USER>/.arien-ai

# Set environment variables
ENV ARIEN_AI_CONFIG_DIR=/home/<USER>/.arien-ai
ENV ARIEN_AI_LOG_LEVEL=info

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD arien-ai config validate || exit 1

# Default command
ENTRYPOINT ["arien-ai"]
CMD ["--help"]

# Labels
LABEL org.opencontainers.image.title="Arien AI"
LABEL org.opencontainers.image.description="Modern CLI Terminal with LLM Integration"
LABEL org.opencontainers.image.version="$VERSION"
LABEL org.opencontainers.image.source="https://github.com/your-username/arien-ai"
LABEL org.opencontainers.image.licenses="MIT"
