package providers

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

// DeepseekProvider implements the Provider interface for Deepseek
type DeepseekProvider struct {
	config ProviderConfig
	client *resty.Client
}

// NewDeepseekProvider creates a new Deepseek provider
func NewDeepseekProvider(config ProviderConfig) (*DeepseekProvider, error) {
	if config.APIKey == "" {
		return nil, fmt.Errorf("API key is required for Deepseek provider")
	}

	if config.BaseURL == "" {
		config.BaseURL = "https://api.deepseek.com"
	}

	client := resty.New()
	client.SetBaseURL(config.BaseURL)
	client.SetHeader("Authorization", "Bearer "+config.APIKey)
	client.SetHeader("Content-Type", "application/json")
	client.SetTimeout(30 * time.Second)

	return &DeepseekProvider{
		config: config,
		client: client,
	}, nil
}

// Name returns the provider name
func (p *DeepseekProvider) Name() string {
	return "deepseek"
}

// Models returns available models
func (p *DeepseekProvider) Models() []string {
	return []string{"deepseek-chat", "deepseek-reasoner"}
}

// Chat sends a chat completion request
func (p *DeepseekProvider) Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	if req.Model == "" {
		req.Model = p.config.Model
	}

	resp, err := p.client.R().
		SetContext(ctx).
		SetBody(req).
		SetResult(&ChatResponse{}).
		Post("/chat/completions")

	if err != nil {
		return nil, fmt.Errorf("failed to send chat request: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("API error: %s", resp.String())
	}

	result := resp.Result().(*ChatResponse)
	return result, nil
}

// Stream sends a streaming chat completion request
func (p *DeepseekProvider) Stream(ctx context.Context, req *ChatRequest) (<-chan *StreamResponse, error) {
	if req.Model == "" {
		req.Model = p.config.Model
	}
	req.Stream = true

	responseChan := make(chan *StreamResponse, 100)

	go func() {
		defer close(responseChan)

		resp, err := p.client.R().
			SetContext(ctx).
			SetBody(req).
			SetDoNotParseResponse(true).
			Post("/chat/completions")

		if err != nil {
			responseChan <- &StreamResponse{Error: fmt.Errorf("failed to send stream request: %w", err)}
			return
		}

		if resp.IsError() {
			responseChan <- &StreamResponse{Error: fmt.Errorf("API error: %s", resp.String())}
			return
		}

		defer resp.RawBody().Close()

		// Parse SSE stream
		scanner := NewSSEScanner(resp.RawBody())
		for scanner.Scan() {
			line := scanner.Text()
			if strings.HasPrefix(line, "data: ") {
				data := strings.TrimPrefix(line, "data: ")
				if data == "[DONE]" {
					responseChan <- &StreamResponse{Done: true}
					return
				}

				var streamResp StreamResponse
				if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
					responseChan <- &StreamResponse{Error: fmt.Errorf("failed to parse stream response: %w", err)}
					continue
				}

				responseChan <- &streamResp
			}
		}

		if err := scanner.Err(); err != nil {
			responseChan <- &StreamResponse{Error: fmt.Errorf("stream reading error: %w", err)}
		}
	}()

	return responseChan, nil
}

// Validate checks if the provider configuration is valid
func (p *DeepseekProvider) Validate() error {
	if p.config.APIKey == "" {
		return fmt.Errorf("API key is required")
	}

	// Test connection with a simple request
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	testReq := &ChatRequest{
		Messages: []Message{
			{Role: "user", Content: "Hello"},
		},
		Model:     p.config.Model,
		MaxTokens: 10,
	}

	_, err := p.Chat(ctx, testReq)
	if err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	return nil
}

// SSEScanner is a simple SSE (Server-Sent Events) scanner
type SSEScanner struct {
	scanner *bufio.Scanner
}

// NewSSEScanner creates a new SSE scanner
func NewSSEScanner(r io.Reader) *SSEScanner {
	return &SSEScanner{
		scanner: bufio.NewScanner(r),
	}
}

// Scan advances the scanner to the next line
func (s *SSEScanner) Scan() bool {
	return s.scanner.Scan()
}

// Text returns the current line
func (s *SSEScanner) Text() string {
	return s.scanner.Text()
}

// Err returns any error encountered during scanning
func (s *SSEScanner) Err() error {
	return s.scanner.Err()
}
