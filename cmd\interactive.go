package cmd

import (
	"fmt"

	"arien-ai/config"
	"arien-ai/providers"
	"arien-ai/session"
	"arien-ai/tools"
	"arien-ai/ui"
	"arien-ai/utils"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/spf13/cobra"
)

// NewInteractiveCmd creates the interactive command
func NewInteractiveCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "interactive",
		Short: "Start interactive terminal mode",
		Long: `Start the interactive terminal mode with real-time AI chat.

This mode provides:
- Real-time chat with AI providers (Deepseek, Ollama)
- Function calling with shell command execution
- Session management and history
- Slash commands for configuration
- Thinking animation and progress indicators`,
		Aliases: []string{"i", "chat", "terminal"},
		RunE:    runInteractive,
	}

	cmd.Flags().StringP("provider", "p", "", "LLM provider to use (deepseek, ollama)")
	cmd.Flags().StringP("model", "m", "", "Model to use")
	cmd.Flags().StringP("session", "s", "", "Session ID to load")
	cmd.Flags().BoolP("new-session", "n", false, "Create a new session")

	return cmd
}

func runInteractive(cmd *cobra.Command, args []string) error {
	cfg := config.Get()

	// Check if configuration is complete
	if !config.IsConfigured() {
		fmt.Println("Configuration incomplete. Please run 'arien-ai onboarding' first.")
		return nil
	}

	// Override provider and model from flags
	provider, _ := cmd.Flags().GetString("provider")
	model, _ := cmd.Flags().GetString("model")
	
	if provider != "" {
		cfg.Provider.Name = provider
	}
	if model != "" {
		cfg.Provider.Model = model
	}

	// Initialize provider
	providerConfig := providers.ProviderConfig{
		Name:    cfg.Provider.Name,
		Model:   cfg.Provider.Model,
		APIKey:  cfg.Provider.APIKey,
		BaseURL: cfg.Provider.BaseURL,
	}

	llmProvider, err := providers.NewProvider(providerConfig)
	if err != nil {
		return fmt.Errorf("failed to initialize provider: %w", err)
	}

	// Validate provider
	if err := llmProvider.Validate(); err != nil {
		return fmt.Errorf("provider validation failed: %w", err)
	}

	// Initialize session manager
	sessionManager, err := session.NewManager()
	if err != nil {
		return fmt.Errorf("failed to initialize session manager: %w", err)
	}

	// Handle session loading/creation
	sessionID, _ := cmd.Flags().GetString("session")
	newSession, _ := cmd.Flags().GetBool("new-session")

	var sess *session.Session
	if newSession || sessionID == "" {
		sess, err = sessionManager.NewSession("", cfg.Provider.Name, cfg.Provider.Model)
		if err != nil {
			return fmt.Errorf("failed to create new session: %w", err)
		}
		utils.LogInfo("Created new session: %s", sess.ID)
	} else {
		sess, err = sessionManager.LoadSession(sessionID)
		if err != nil {
			return fmt.Errorf("failed to load session: %w", err)
		}
		utils.LogInfo("Loaded session: %s", sess.ID)
	}

	// Initialize tool manager
	toolManager := tools.NewToolManager()

	// Create terminal model
	terminalModel := ui.NewTerminalModel(llmProvider, toolManager, sess)

	// Start the terminal UI
	program := tea.NewProgram(
		terminalModel,
		tea.WithAltScreen(),
		tea.WithMouseCellMotion(),
	)

	utils.LogInfo("Starting interactive terminal mode")
	
	if _, err := program.Run(); err != nil {
		return fmt.Errorf("failed to run terminal: %w", err)
	}

	// Save session before exit
	if err := sessionManager.SaveSession(sess); err != nil {
		utils.LogWarn("Failed to save session: %v", err)
	}

	return nil
}
