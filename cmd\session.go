package cmd

import (
	"fmt"
	"os"
	"text/tabwriter"
	"time"

	"arien-ai/session"

	"github.com/spf13/cobra"
)

// NewSessionCmd creates the session command
func NewSessionCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "session",
		Short: "Manage chat sessions",
		Long: `Manage chat sessions for conversation history.

Subcommands:
- list: List all sessions
- show: Show session details
- delete: Delete a session
- new: Create a new session`,
	}

	cmd.AddCommand(newSessionListCmd())
	cmd.AddCommand(newSessionShowCmd())
	cmd.AddCommand(newSessionDeleteCmd())
	cmd.AddCommand(newSessionNewCmd())

	return cmd
}

func newSessionListCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list",
		Short: "List all sessions",
		Aliases: []string{"ls"},
		RunE: func(cmd *cobra.Command, args []string) error {
			manager, err := session.NewManager()
			if err != nil {
				return fmt.Errorf("failed to create session manager: %w", err)
			}

			sessions, err := manager.ListSessions()
			if err != nil {
				return fmt.Errorf("failed to list sessions: %w", err)
			}

			if len(sessions) == 0 {
				fmt.Println("No sessions found")
				return nil
			}

			// Create table writer
			w := tabwriter.NewWriter(os.Stdout, 0, 0, 2, ' ', 0)
			fmt.Fprintln(w, "ID\tNAME\tCREATED\tUPDATED\tMESSAGES\tPROVIDER\tMODEL")
			fmt.Fprintln(w, "---\t----\t-------\t-------\t--------\t--------\t-----")

			for _, sess := range sessions {
				fmt.Fprintf(w, "%s\t%s\t%s\t%s\t%d\t%s\t%s\n",
					sess.ID[:8],
					sess.Name,
					sess.CreatedAt.Format("2006-01-02 15:04"),
					sess.UpdatedAt.Format("2006-01-02 15:04"),
					len(sess.Messages),
					sess.Metadata.Provider,
					sess.Metadata.Model,
				)
			}

			w.Flush()
			return nil
		},
	}

	return cmd
}

func newSessionShowCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "show [session-id]",
		Short: "Show session details",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			sessionID := args[0]

			manager, err := session.NewManager()
			if err != nil {
				return fmt.Errorf("failed to create session manager: %w", err)
			}

			sess, err := manager.LoadSession(sessionID)
			if err != nil {
				return fmt.Errorf("failed to load session: %w", err)
			}

			// Show session info
			fmt.Printf("Session ID: %s\n", sess.ID)
			fmt.Printf("Name: %s\n", sess.Name)
			fmt.Printf("Created: %s\n", sess.CreatedAt.Format("2006-01-02 15:04:05"))
			fmt.Printf("Updated: %s\n", sess.UpdatedAt.Format("2006-01-02 15:04:05"))
			fmt.Printf("Provider: %s\n", sess.Metadata.Provider)
			fmt.Printf("Model: %s\n", sess.Metadata.Model)
			fmt.Printf("Messages: %d\n", len(sess.Messages))
			fmt.Println()

			// Show recent messages
			if len(sess.Messages) > 0 {
				fmt.Println("Recent Messages:")
				fmt.Println("================")

				// Show last 10 messages
				start := 0
				if len(sess.Messages) > 10 {
					start = len(sess.Messages) - 10
					fmt.Printf("... (showing last 10 of %d messages)\n\n", len(sess.Messages))
				}

				for i := start; i < len(sess.Messages); i++ {
					msg := sess.Messages[i]
					timestamp := msg.Timestamp.Format("15:04:05")
					
					switch msg.Role {
					case "user":
						fmt.Printf("[%s] You: %s\n", timestamp, msg.Content)
					case "assistant":
						fmt.Printf("[%s] AI: %s\n", timestamp, msg.Content)
					case "system":
						fmt.Printf("[%s] System: %s\n", timestamp, msg.Content)
					}
				}
			}

			return nil
		},
	}

	return cmd
}

func newSessionDeleteCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "delete [session-id]",
		Short: "Delete a session",
		Aliases: []string{"del", "rm"},
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			sessionID := args[0]

			manager, err := session.NewManager()
			if err != nil {
				return fmt.Errorf("failed to create session manager: %w", err)
			}

			// Check if session exists
			_, err = manager.LoadSession(sessionID)
			if err != nil {
				return fmt.Errorf("session not found: %s", sessionID)
			}

			if err := manager.DeleteSession(sessionID); err != nil {
				return fmt.Errorf("failed to delete session: %w", err)
			}

			fmt.Printf("Session %s deleted successfully\n", sessionID)
			return nil
		},
	}

	return cmd
}

func newSessionNewCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "new [name]",
		Short: "Create a new session",
		Args:  cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			var name string
			if len(args) > 0 {
				name = args[0]
			} else {
				name = fmt.Sprintf("Session %s", time.Now().Format("2006-01-02 15:04"))
			}

			manager, err := session.NewManager()
			if err != nil {
				return fmt.Errorf("failed to create session manager: %w", err)
			}

			sess, err := manager.NewSession(name, "deepseek", "deepseek-chat")
			if err != nil {
				return fmt.Errorf("failed to create session: %w", err)
			}

			fmt.Printf("Created new session: %s\n", sess.ID)
			fmt.Printf("Name: %s\n", sess.Name)
			fmt.Printf("Use 'arien-ai interactive --session %s' to start chatting\n", sess.ID)

			return nil
		},
	}

	return cmd
}
