package utils

import (
	"context"
	"fmt"
	"math/rand/v2"
	"net"
	"strings"
	"time"

	"arien-ai/config"
)

// RetryableError represents an error that can be retried
type RetryableError struct {
	Err       error
	Retryable bool
}

func (e *RetryableError) Error() string {
	return e.Err.Error()
}

func (e *RetryableError) Unwrap() error {
	return e.Err
}

// IsRetryable checks if an error is retryable
func IsRetryable(err error) bool {
	if err == nil {
		return false
	}

	// Network errors are generally retryable
	if _, ok := err.(net.Error); ok {
		return true
	}

	// Timeout errors are retryable
	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		return true
	}

	// Context deadline exceeded is retryable
	if err == context.DeadlineExceeded {
		return true
	}

	// Check for specific error messages that indicate temporary issues
	errMsg := err.Error()
	retryableMessages := []string{
		"connection refused",
		"connection reset",
		"timeout",
		"rate limit",
		"too many requests",
		"service unavailable",
		"internal server error",
		"bad gateway",
		"gateway timeout",
	}

	for _, msg := range retryableMessages {
		if strings.Contains(strings.ToLower(errMsg), msg) {
			return true
		}
	}

	return false
}

// RetryConfig holds retry configuration
type RetryConfig struct {
	MaxRetries    int
	InitialDelay  time.Duration
	MaxDelay      time.Duration
	BackoffFactor float64
}

// GetRetryConfig returns the retry configuration from app config
func GetRetryConfig() RetryConfig {
	cfg := config.Get()
	return RetryConfig{
		MaxRetries:    cfg.RetryPolicy.MaxRetries,
		InitialDelay:  time.Duration(cfg.RetryPolicy.InitialDelay) * time.Millisecond,
		MaxDelay:      time.Duration(cfg.RetryPolicy.MaxDelay) * time.Millisecond,
		BackoffFactor: float64(cfg.RetryPolicy.BackoffFactor),
	}
}

// RetryFunc represents a function that can be retried
type RetryFunc func() error

// Retry executes a function with exponential backoff retry logic
func Retry(ctx context.Context, fn RetryFunc) error {
	return RetryWithConfig(ctx, fn, GetRetryConfig())
}

// RetryWithConfig executes a function with custom retry configuration
func RetryWithConfig(ctx context.Context, fn RetryFunc, config RetryConfig) error {
	var lastErr error
	delay := config.InitialDelay

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		// Execute the function
		err := fn()
		if err == nil {
			return nil
		}

		lastErr = err

		// Check if error is retryable
		if !IsRetryable(err) {
			LogDebug("Error is not retryable, giving up: %v", err)
			return err
		}

		// Don't retry on the last attempt
		if attempt == config.MaxRetries {
			break
		}

		LogDebug("Attempt %d failed, retrying in %v: %v", attempt+1, delay, err)

		// Wait with context cancellation support
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
		}

		// Calculate next delay with exponential backoff
		delay = time.Duration(float64(delay) * config.BackoffFactor)
		if delay > config.MaxDelay {
			delay = config.MaxDelay
		}
	}

	return fmt.Errorf("max retries (%d) exceeded, last error: %w", config.MaxRetries, lastErr)
}

// RetryWithJitter executes a function with exponential backoff and jitter
func RetryWithJitter(ctx context.Context, fn RetryFunc) error {
	config := GetRetryConfig()
	var lastErr error
	delay := config.InitialDelay

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		err := fn()
		if err == nil {
			return nil
		}

		lastErr = err

		if !IsRetryable(err) {
			return err
		}

		if attempt == config.MaxRetries {
			break
		}

		// Add jitter to prevent thundering herd
		jitter := time.Duration(float64(delay) * 0.1 * (2*rand.Float64() - 1))
		actualDelay := delay + jitter

		LogDebug("Attempt %d failed, retrying in %v (with jitter): %v", attempt+1, actualDelay, err)

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(actualDelay):
		}

		delay = time.Duration(float64(delay) * config.BackoffFactor)
		if delay > config.MaxDelay {
			delay = config.MaxDelay
		}
	}

	return fmt.Errorf("max retries (%d) exceeded, last error: %w", config.MaxRetries, lastErr)
}
