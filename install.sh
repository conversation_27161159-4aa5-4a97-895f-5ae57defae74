#!/bin/bash

# Arien AI Installation Script
# Supports Windows WSL, macOS, and Linux
# Version: 1.0.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BINARY_NAME="arien-ai"
INSTALL_DIR="/usr/local/bin"
REPO_URL="https://github.com/your-username/arien-ai"
VERSION="latest"
GO_MIN_VERSION="1.24"

# Functions
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Arien AI Installation                     ║"
    echo "║              Modern CLI Terminal with LLM Integration        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if grep -q Microsoft /proc/version 2>/dev/null; then
            echo "wsl"
        else
            echo "linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    else
        echo "unknown"
    fi
}

detect_arch() {
    case $(uname -m) in
        x86_64) echo "amd64" ;;
        arm64|aarch64) echo "arm64" ;;
        *) echo "unknown" ;;
    esac
}

check_dependencies() {
    local missing_deps=()
    
    # Check for required tools
    if ! command -v curl >/dev/null 2>&1 && ! command -v wget >/dev/null 2>&1; then
        missing_deps+=("curl or wget")
    fi
    
    if ! command -v tar >/dev/null 2>&1; then
        missing_deps+=("tar")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing required dependencies: ${missing_deps[*]}"
        print_info "Please install the missing dependencies and try again."
        exit 1
    fi
}

check_go_installation() {
    if ! command -v go >/dev/null 2>&1; then
        print_error "Go is not installed or not in PATH"
        print_info "Please install Go ${GO_MIN_VERSION}+ from https://golang.org/dl/"
        print_info "Or use the binary installation method instead"
        return 1
    fi

    local go_version=$(go version | grep -oE 'go[0-9]+\.[0-9]+(\.[0-9]+)?' | sed 's/go//')
    local major=$(echo $go_version | cut -d. -f1)
    local minor=$(echo $go_version | cut -d. -f2)
    local min_major=$(echo $GO_MIN_VERSION | cut -d. -f1)
    local min_minor=$(echo $GO_MIN_VERSION | cut -d. -f2)

    if [ "$major" -lt "$min_major" ] || ([ "$major" -eq "$min_major" ] && [ "$minor" -lt "$min_minor" ]); then
        print_error "Go version $go_version is too old. Minimum required: ${GO_MIN_VERSION}"
        print_info "Please upgrade Go or use the binary installation method"
        return 1
    fi

    print_success "Go version $go_version detected"
    return 0
}

check_system_requirements() {
    print_step "Checking system requirements..."

    local os=$(detect_os)
    local arch=$(detect_arch)

    print_info "Detected OS: $os"
    print_info "Detected Architecture: $arch"

    # Check available disk space (at least 100MB)
    local available_space
    if command -v df >/dev/null 2>&1; then
        available_space=$(df /tmp | tail -1 | awk '{print $4}')
        if [ "$available_space" -lt 102400 ]; then  # 100MB in KB
            print_warning "Low disk space detected. At least 100MB recommended."
        fi
    fi

    # Check internet connectivity
    if command -v ping >/dev/null 2>&1; then
        if ! ping -c 1 google.com >/dev/null 2>&1; then
            print_warning "Internet connectivity check failed. Installation may fail."
        fi
    fi

    print_success "System requirements check completed"
}

install_from_source() {
    print_step "Installing Arien AI from source..."

    # Check Go installation
    if ! check_go_installation; then
        print_error "Go installation check failed"
        exit 1
    fi

    local temp_dir=$(mktemp -d)
    print_info "Using temporary directory: $temp_dir"
    cd "$temp_dir"

    # Check if we're in a git repository (local development)
    if [ -f "../main.go" ] && [ -f "../go.mod" ]; then
        print_info "Building from local source..."
        cp -r ../* .
    else
        print_info "Cloning repository from $REPO_URL..."
        if command -v git >/dev/null 2>&1; then
            if ! git clone "$REPO_URL" .; then
                print_error "Failed to clone repository"
                print_info "Please check your internet connection and repository URL"
                exit 1
            fi
        else
            print_error "Git is not installed. Please install git or use binary installation."
            exit 1
        fi
    fi

    print_info "Downloading Go dependencies..."
    if ! go mod download; then
        print_error "Failed to download Go dependencies"
        exit 1
    fi

    print_info "Running tests..."
    if ! go test ./...; then
        print_warning "Some tests failed, but continuing with installation..."
    fi

    print_info "Building binary..."
    local build_flags="-ldflags"
    local ldflags="-X main.version=$VERSION"

    if command -v git >/dev/null 2>&1; then
        ldflags="$ldflags -X main.commit=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
    fi

    ldflags="$ldflags -X main.date=$(date -u +%Y-%m-%dT%H:%M:%SZ)"

    # Build for current platform
    local os=$(detect_os)
    local arch=$(detect_arch)

    if [ "$os" == "wsl" ]; then
        os="linux"
    fi

    print_info "Building for $os/$arch..."
    if ! GOOS=$os GOARCH=$arch go build $build_flags "$ldflags" -o "$BINARY_NAME"; then
        print_error "Failed to build binary"
        exit 1
    fi

    print_info "Installing binary to $INSTALL_DIR..."
    install_binary_to_system "$temp_dir/$BINARY_NAME"

    # Cleanup
    cd /
    rm -rf "$temp_dir"

    print_success "Arien AI installed successfully from source!"
}

install_binary_to_system() {
    local binary_path="$1"

    # Create install directory if it doesn't exist
    if [ ! -d "$INSTALL_DIR" ]; then
        print_info "Creating install directory: $INSTALL_DIR"
        if ! sudo mkdir -p "$INSTALL_DIR" 2>/dev/null; then
            if ! mkdir -p "$INSTALL_DIR" 2>/dev/null; then
                print_error "Failed to create install directory: $INSTALL_DIR"
                exit 1
            fi
        fi
    fi

    # Copy binary
    if [ -w "$INSTALL_DIR" ]; then
        cp "$binary_path" "$INSTALL_DIR/$BINARY_NAME"
    else
        sudo cp "$binary_path" "$INSTALL_DIR/$BINARY_NAME"
    fi

    # Make executable
    if [ -w "$INSTALL_DIR/$BINARY_NAME" ]; then
        chmod +x "$INSTALL_DIR/$BINARY_NAME"
    else
        sudo chmod +x "$INSTALL_DIR/$BINARY_NAME"
    fi

    # Verify installation
    if [ ! -x "$INSTALL_DIR/$BINARY_NAME" ]; then
        print_error "Binary installation verification failed"
        exit 1
    fi

    print_success "Binary installed to $INSTALL_DIR/$BINARY_NAME"
}

install_from_binary() {
    local os=$(detect_os)
    local arch=$(detect_arch)
    
    if [ "$os" == "unknown" ] || [ "$arch" == "unknown" ]; then
        print_error "Unsupported platform: $OSTYPE $(uname -m)"
        print_info "Falling back to source installation..."
        install_from_source
        return
    fi
    
    print_info "Installing Arien AI binary for $os-$arch..."
    
    local download_url="${REPO_URL}/releases/latest/download/${BINARY_NAME}-${os}-${arch}"
    if [ "$os" == "wsl" ]; then
        download_url="${REPO_URL}/releases/latest/download/${BINARY_NAME}-linux-${arch}"
    fi
    
    local temp_file=$(mktemp)
    
    print_info "Downloading from $download_url..."
    if command -v curl >/dev/null 2>&1; then
        curl -L -o "$temp_file" "$download_url"
    elif command -v wget >/dev/null 2>&1; then
        wget -O "$temp_file" "$download_url"
    fi
    
    if [ ! -s "$temp_file" ]; then
        print_warning "Binary download failed, falling back to source installation..."
        rm -f "$temp_file"
        install_from_source
        return
    fi
    
    print_info "Installing binary to $INSTALL_DIR..."
    if [ -w "$INSTALL_DIR" ]; then
        cp "$temp_file" "$INSTALL_DIR/$BINARY_NAME"
    else
        sudo cp "$temp_file" "$INSTALL_DIR/$BINARY_NAME"
    fi
    
    chmod +x "$INSTALL_DIR/$BINARY_NAME"
    rm -f "$temp_file"
    
    print_success "Arien AI installed successfully!"
}

update_installation() {
    print_info "Updating existing Arien AI installation..."
    
    if [ ! -f "$INSTALL_DIR/$BINARY_NAME" ]; then
        print_error "Arien AI is not installed in $INSTALL_DIR"
        exit 1
    fi
    
    # Backup current version
    local backup_file="$INSTALL_DIR/${BINARY_NAME}.backup"
    if [ -w "$INSTALL_DIR" ]; then
        cp "$INSTALL_DIR/$BINARY_NAME" "$backup_file"
    else
        sudo cp "$INSTALL_DIR/$BINARY_NAME" "$backup_file"
    fi
    
    print_info "Backed up current version to $backup_file"
    
    # Install new version
    if [ "$INSTALL_METHOD" == "source" ]; then
        install_from_source
    else
        install_from_binary
    fi
    
    # Test new installation
    if "$INSTALL_DIR/$BINARY_NAME" --version >/dev/null 2>&1; then
        print_success "Update completed successfully!"
        rm -f "$backup_file"
    else
        print_error "Update failed, restoring backup..."
        if [ -w "$INSTALL_DIR" ]; then
            cp "$backup_file" "$INSTALL_DIR/$BINARY_NAME"
        else
            sudo cp "$backup_file" "$INSTALL_DIR/$BINARY_NAME"
        fi
        rm -f "$backup_file"
        exit 1
    fi
}

uninstall() {
    print_info "Uninstalling Arien AI..."
    
    if [ -f "$INSTALL_DIR/$BINARY_NAME" ]; then
        if [ -w "$INSTALL_DIR" ]; then
            rm "$INSTALL_DIR/$BINARY_NAME"
        else
            sudo rm "$INSTALL_DIR/$BINARY_NAME"
        fi
        print_success "Binary removed from $INSTALL_DIR"
    else
        print_warning "Binary not found in $INSTALL_DIR"
    fi
    
    # Remove configuration (optional)
    local config_dir="$HOME/.arien-ai"
    if [ -d "$config_dir" ]; then
        read -p "Remove configuration directory $config_dir? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$config_dir"
            print_success "Configuration directory removed"
        fi
    fi
    
    print_success "Arien AI uninstalled successfully!"
}

show_usage() {
    print_banner
    echo ""
    echo "Usage: $0 [ACTION] [OPTIONS]"
    echo ""
    echo "Actions:"
    echo "  install          Install Arien AI (default)"
    echo "  update           Update existing installation"
    echo "  uninstall        Remove Arien AI completely"
    echo "  check            Check system requirements"
    echo ""
    echo "Options:"
    echo "  --source         Install from source code (requires Go ${GO_MIN_VERSION}+)"
    echo "  --binary         Install from pre-built binary (default)"
    echo "  --version VER    Install specific version (default: latest)"
    echo "  --install-dir DIR Install to custom directory (default: $INSTALL_DIR)"
    echo "  --force          Force installation even if already installed"
    echo "  --quiet          Suppress non-essential output"
    echo "  --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                           # Install latest binary"
    echo "  $0 install --source         # Install from source"
    echo "  $0 update                   # Update existing installation"
    echo "  $0 uninstall                # Remove installation"
    echo "  $0 check                    # Check system requirements"
    echo "  $0 --version v1.0.0         # Install specific version"
    echo "  $0 --install-dir ~/.local/bin # Install to custom directory"
    echo ""
    echo "Supported Platforms:"
    echo "  - Linux (x86_64, arm64)"
    echo "  - macOS (x86_64, arm64)"
    echo "  - Windows WSL (x86_64, arm64)"
    echo ""
    echo "For more information, visit: $REPO_URL"
}

check_existing_installation() {
    if [ -f "$INSTALL_DIR/$BINARY_NAME" ]; then
        local current_version
        current_version=$("$INSTALL_DIR/$BINARY_NAME" --version 2>/dev/null | head -1 || echo "unknown")
        print_info "Existing installation found: $current_version"
        return 0
    fi
    return 1
}

post_install_setup() {
    print_step "Post-installation setup..."

    # Check if binary is in PATH
    if ! command -v "$BINARY_NAME" >/dev/null 2>&1; then
        print_warning "$BINARY_NAME is not in your PATH"
        print_info "Add $INSTALL_DIR to your PATH by adding this line to your shell profile:"
        print_info "export PATH=\"$INSTALL_DIR:\$PATH\""

        # Try to add to common shell profiles
        local shell_profile=""
        if [ -n "$BASH_VERSION" ]; then
            shell_profile="$HOME/.bashrc"
        elif [ -n "$ZSH_VERSION" ]; then
            shell_profile="$HOME/.zshrc"
        fi

        if [ -n "$shell_profile" ] && [ -w "$shell_profile" ]; then
            echo "export PATH=\"$INSTALL_DIR:\$PATH\"" >> "$shell_profile"
            print_success "Added $INSTALL_DIR to PATH in $shell_profile"
            print_info "Please restart your shell or run: source $shell_profile"
        fi
    fi

    # Test installation
    if "$INSTALL_DIR/$BINARY_NAME" --version >/dev/null 2>&1; then
        print_success "Installation verified successfully!"
    else
        print_warning "Installation verification failed"
    fi
}

# Main script
main() {
    local action="install"
    local install_method="binary"
    local force_install=false
    local quiet=false

    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            install)
                action="install"
                shift
                ;;
            update)
                action="update"
                shift
                ;;
            uninstall)
                action="uninstall"
                shift
                ;;
            check)
                action="check"
                shift
                ;;
            --source)
                install_method="source"
                shift
                ;;
            --binary)
                install_method="binary"
                shift
                ;;
            --version)
                VERSION="$2"
                shift 2
                ;;
            --install-dir)
                INSTALL_DIR="$2"
                shift 2
                ;;
            --force)
                force_install=true
                shift
                ;;
            --quiet)
                quiet=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    export INSTALL_METHOD="$install_method"

    # Show banner unless quiet
    if [ "$quiet" = false ]; then
        print_banner
        echo ""
        print_info "Action: $action"
        print_info "Method: $install_method"
        print_info "Version: $VERSION"
        print_info "Install Directory: $INSTALL_DIR"
        echo ""
    fi

    case $action in
        check)
            check_system_requirements
            check_dependencies
            if [ "$install_method" == "source" ]; then
                check_go_installation
            fi
            print_success "System check completed"
            exit 0
            ;;
        install)
            # Check for existing installation
            if check_existing_installation && [ "$force_install" = false ]; then
                print_warning "Arien AI is already installed"
                print_info "Use 'update' action to update or --force to reinstall"
                exit 0
            fi

            check_system_requirements
            check_dependencies

            if [ "$install_method" == "source" ]; then
                install_from_source
            else
                install_from_binary
            fi

            post_install_setup
            ;;
        update)
            update_installation
            ;;
        uninstall)
            uninstall
            exit 0
            ;;
    esac

    # Show next steps
    if [ "$action" != "uninstall" ] && [ "$quiet" = false ]; then
        echo ""
        print_step "Next steps:"
        echo "1. Run '$BINARY_NAME onboarding' to set up your configuration"
        echo "2. Run '$BINARY_NAME interactive' to start the AI terminal"
        echo "3. Use '$BINARY_NAME --help' to see all available commands"
        echo ""
        print_info "For support and documentation, visit: $REPO_URL"
    fi
}

# Run main function
main "$@"
