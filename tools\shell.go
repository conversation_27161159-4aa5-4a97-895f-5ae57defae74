package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"

	"arien-ai/providers"
)

// ShellTool implements shell command execution functionality
type ShellTool struct {
	name        string
	description string
	parameters  map[string]interface{}
}

// NewShellTool creates a new shell tool instance
func NewShellTool() *ShellTool {
	return &ShellTool{
		name: "execute_shell_command",
		description: `Execute shell commands and capture their output.

USAGE GUIDELINES:
- Use this tool to run system commands, file operations, and process management
- Always explain what the command will do before executing it
- For potentially destructive operations, ask for user confirmation
- Use appropriate shell syntax for the target operating system

WHEN TO USE:
- File and directory operations (ls, mkdir, cp, mv, rm, etc.)
- System information gathering (ps, top, df, free, etc.)
- Package management (apt, yum, brew, pip, npm, etc.)
- Git operations (git status, git add, git commit, etc.)
- Network operations (ping, curl, wget, etc.)
- Text processing (grep, sed, awk, etc.)
- Process management (kill, killall, etc.)

WHEN NOT TO USE:
- For simple calculations (use built-in capabilities instead)
- For text generation or analysis (use LLM capabilities instead)
- For operations that require interactive input (use non-interactive alternatives)

PARALLEL vs SEQUENTIAL EXECUTION:
- Use PARALLEL execution for:
  * Independent operations that can run simultaneously
  * Multiple file downloads or uploads
  * Parallel compilation or testing
  * Concurrent system checks

- Use SEQUENTIAL execution for:
  * Commands that depend on previous command results
  * Operations that modify the same files or resources
  * Commands that require specific order (setup -> configure -> run)
  * Operations that might conflict if run simultaneously

EXAMPLES:
1. File Operations:
   - List files: "ls -la" or "dir" (Windows)
   - Create directory: "mkdir new_folder"
   - Copy files: "cp source.txt dest.txt"

2. System Information:
   - Check disk space: "df -h" or "dir" (Windows)
   - List processes: "ps aux" or "tasklist" (Windows)
   - Check memory: "free -h" or "systeminfo" (Windows)

3. Package Management:
   - Install package: "npm install package-name"
   - Update packages: "apt update && apt upgrade"
   - List installed: "pip list"

4. Git Operations:
   - Check status: "git status"
   - Add files: "git add ."
   - Commit changes: "git commit -m 'message'"

5. Network Operations:
   - Test connectivity: "ping google.com"
   - Download file: "curl -O https://example.com/file.txt"
   - Check ports: "netstat -tulpn"

SAFETY CONSIDERATIONS:
- Always validate command syntax before execution
- Be cautious with rm, del, format, and other destructive commands
- Use --dry-run or similar flags when available for testing
- Avoid commands that require sudo/admin privileges without explicit user consent
- Be aware of the current working directory context

OUTPUT PROCESSING:
- Capture both stdout and stderr
- Parse command exit codes for success/failure detection
- Format output for readability
- Highlight important information or errors
- Provide context for command results`,
		parameters: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"command": map[string]interface{}{
					"type":        "string",
					"description": "The shell command to execute",
				},
				"working_directory": map[string]interface{}{
					"type":        "string",
					"description": "Working directory for command execution (optional)",
				},
				"timeout": map[string]interface{}{
					"type":        "integer",
					"description": "Command timeout in seconds (default: 30)",
				},
				"capture_output": map[string]interface{}{
					"type":        "boolean",
					"description": "Whether to capture command output (default: true)",
				},
			},
			"required": []string{"command"},
		},
	}
}

// GetDefinition returns the tool definition for function calling
func (t *ShellTool) GetDefinition() providers.Tool {
	return providers.Tool{
		Type: "function",
		Function: providers.ToolFunction{
			Name:        t.name,
			Description: t.description,
			Parameters:  t.parameters,
		},
	}
}

// Execute runs the shell command and returns the result
func (t *ShellTool) Execute(ctx context.Context, args string) (*ToolResult, error) {
	var params struct {
		Command          string `json:"command"`
		WorkingDirectory string `json:"working_directory,omitempty"`
		Timeout          int    `json:"timeout,omitempty"`
		CaptureOutput    bool   `json:"capture_output,omitempty"`
	}

	if err := json.Unmarshal([]byte(args), &params); err != nil {
		return nil, fmt.Errorf("failed to parse arguments: %w", err)
	}

	if params.Command == "" {
		return nil, fmt.Errorf("command is required")
	}

	// Set defaults
	if params.Timeout == 0 {
		params.Timeout = 30
	}
	if !params.CaptureOutput {
		params.CaptureOutput = true
	}

	// Create command context with timeout
	cmdCtx, cancel := context.WithTimeout(ctx, time.Duration(params.Timeout)*time.Second)
	defer cancel()

	// Prepare command based on OS
	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "windows":
		cmd = exec.CommandContext(cmdCtx, "powershell", "-Command", params.Command)
	default:
		cmd = exec.CommandContext(cmdCtx, "sh", "-c", params.Command)
	}

	// Set working directory if specified
	if params.WorkingDirectory != "" {
		if _, err := os.Stat(params.WorkingDirectory); err != nil {
			return &ToolResult{
				Success: false,
				Output:  fmt.Sprintf("Working directory does not exist: %s", params.WorkingDirectory),
				Error:   err.Error(),
			}, nil
		}
		cmd.Dir = params.WorkingDirectory
	}

	// Execute command
	startTime := time.Now()
	var output []byte
	var err error

	if params.CaptureOutput {
		output, err = cmd.CombinedOutput()
	} else {
		err = cmd.Run()
	}

	duration := time.Since(startTime)

	// Prepare result
	result := &ToolResult{
		Success:  err == nil,
		Duration: duration,
		Metadata: map[string]interface{}{
			"command":           params.Command,
			"working_directory": cmd.Dir,
			"timeout":           params.Timeout,
			"duration_ms":       duration.Milliseconds(),
			"os":                runtime.GOOS,
		},
	}

	if params.CaptureOutput {
		result.Output = string(output)
	}

	if err != nil {
		result.Error = err.Error()
		
		// Check for specific error types
		if cmdCtx.Err() == context.DeadlineExceeded {
			result.Error = fmt.Sprintf("Command timed out after %d seconds", params.Timeout)
		}
	}

	// Add exit code if available
	if exitError, ok := err.(*exec.ExitError); ok {
		result.Metadata["exit_code"] = exitError.ExitCode()
	} else if err == nil {
		result.Metadata["exit_code"] = 0
	}

	return result, nil
}

// Name returns the tool name
func (t *ShellTool) Name() string {
	return t.name
}

// Description returns the tool description
func (t *ShellTool) Description() string {
	return t.description
}

// ValidateCommand performs basic validation on shell commands
func ValidateCommand(command string) error {
	command = strings.TrimSpace(command)
	if command == "" {
		return fmt.Errorf("command cannot be empty")
	}

	// Check for potentially dangerous commands
	dangerousCommands := []string{
		"rm -rf /", "del /f /s /q C:\\", "format", "fdisk",
		"dd if=/dev/zero", ":(){ :|:& };:", "sudo rm -rf",
	}

	lowerCmd := strings.ToLower(command)
	for _, dangerous := range dangerousCommands {
		if strings.Contains(lowerCmd, strings.ToLower(dangerous)) {
			return fmt.Errorf("potentially dangerous command detected: %s", dangerous)
		}
	}

	return nil
}
