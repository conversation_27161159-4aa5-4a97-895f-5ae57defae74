# Arien AI - Modern CLI Terminal with LLM Integration

Arien AI is a powerful CLI terminal system that integrates with LLM providers to execute shell commands intelligently and complete user tasks through function calling.

## Features

### 🚀 Core Capabilities
- **Function Calling**: Execute shell commands with AI guidance
- **Multi-Provider Support**: Deepseek and Ollama integration
- **Interactive Terminal**: Real-time chat with thinking animations
- **Session Management**: Persistent conversation history
- **Error Handling**: Robust retry logic and error recovery
- **Cross-Platform**: Windows WSL, macOS, and Linux support

### 🎯 LLM Providers
- **Deepseek**: Support for `deepseek-chat` and `deepseek-reasoner` models
- **Ollama**: Local model support with automatic model detection

### 🛠️ Function Tools
- **Shell Command Execution**: Comprehensive command execution with safety checks
- **Output Processing**: Intelligent parsing and formatting of command results
- **Parallel/Sequential Execution**: Smart execution strategies based on task requirements

### 🎨 Terminal UI Components
- **Onboarding**: Interactive setup for providers and configuration
- **Main Terminal**: Full-featured chat interface with real-time responses
- **Message History**: Persistent conversation tracking
- **Thinking Animation**: Visual feedback during AI processing
- **Slash Commands**: Quick configuration and session management

## Installation

### Quick Install (Recommended)

```bash
# Install latest version
curl -fsSL https://raw.githubusercontent.com/your-username/arien-ai/main/install.sh | bash

# Or with wget
wget -qO- https://raw.githubusercontent.com/your-username/arien-ai/main/install.sh | bash
```

### Install Options

**Linux/macOS/WSL:**
```bash
# Install from source
./install.sh --source

# Install to custom directory
./install.sh --install-dir /opt/bin

# Update existing installation
./install.sh update

# Uninstall
./install.sh uninstall
```

**Windows (PowerShell):**
```powershell
# Download and run install script
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/your-username/arien-ai/main/install.ps1" -OutFile "install.ps1"
.\install.ps1

# Install from source
.\install.ps1 -InstallMethod source

# Install to custom directory
.\install.ps1 -InstallDir "C:\Tools\arien-ai"

# Update existing installation
.\install.ps1 update

# Uninstall
.\install.ps1 uninstall
```

### Manual Installation

1. **Prerequisites**: Go 1.24+ required
2. **Clone and build**:
   ```bash
   git clone https://github.com/your-username/arien-ai.git
   cd arien-ai
   go build -o arien-ai
   sudo mv arien-ai /usr/local/bin/
   ```

## Quick Start

### 1. Initial Setup
```bash
# Run interactive onboarding
arien-ai onboarding

# Or configure manually
arien-ai config set provider deepseek
arien-ai config set api-key your-api-key
```

### 2. Start Interactive Mode
```bash
# Start with default configuration
arien-ai interactive

# Start with specific provider/model
arien-ai interactive --provider ollama --model llama3.2

# Load existing session
arien-ai interactive --session session-id
```

### 3. Basic Usage
```bash
# List files and analyze disk usage
> Can you show me the files in this directory and check disk space?

# Install and configure a development environment
> Set up a Python development environment with virtual env and install pandas

# System maintenance
> Check system health, update packages, and clean up temporary files
```

## Configuration

### Provider Configuration

#### Deepseek Setup
```bash
arien-ai config set provider deepseek
arien-ai config set model deepseek-chat
arien-ai config set api-key your-deepseek-api-key
arien-ai config set base-url https://api.deepseek.com
```

#### Ollama Setup
```bash
arien-ai config set provider ollama
arien-ai config set model llama3.2
arien-ai config set base-url http://localhost:11434
```

### Configuration File
Configuration is stored in `~/.arien-ai.yaml`:

```yaml
provider:
  name: deepseek
  model: deepseek-chat
  api_key: your-api-key
  base_url: https://api.deepseek.com

session:
  save_history: true
  max_history: 1000
  session_timeout: 3600

terminal:
  theme: default
  show_timestamps: true
  auto_approve: false
  confirm_commands: true

logging:
  level: info
  console: true
  format: text

retry:
  max_retries: 3
  initial_delay: 1000
  max_delay: 30000
  backoff_factor: 2
```

## Commands

### Main Commands
```bash
arien-ai interactive          # Start interactive terminal
arien-ai onboarding          # Initial setup wizard
arien-ai config              # Manage configuration
arien-ai session             # Manage chat sessions
```

### Configuration Commands
```bash
arien-ai config show         # Display current config
arien-ai config set provider deepseek
arien-ai config set model deepseek-chat
arien-ai config validate     # Test configuration
arien-ai config reset        # Reset to defaults
```

### Session Management
```bash
arien-ai session list        # List all sessions
arien-ai session show <id>   # Show session details
arien-ai session new [name]  # Create new session
arien-ai session delete <id> # Delete session
```

## Slash Commands

Within the interactive terminal, you can use these commands:

```bash
/help                        # Show available commands
/model [model_name]          # Change current model (with validation)
/provider [provider_name]    # Change current provider (with validation)
/session [new|list|load <id>|delete <id>] # Full session management
/message-history             # Show current session message history
/clear                       # Clear message history
/config                      # Show current configuration
/quit                        # Exit application
```

### Enhanced Slash Command Features

- **Model Switching**: `/model deepseek-reasoner` - Validates model availability for current provider
- **Provider Switching**: `/provider ollama` - Automatically sets default model for new provider
- **Session Management**:
  - `/session new` - Creates a new session
  - `/session list` - Shows all available sessions with details
  - `/session load <id>` - Instructions to load a specific session
  - `/session delete <id>` - Permanently deletes a session
- **Real-time Updates**: Configuration changes are saved immediately

## Function Calling

### Shell Command Tool

The shell command tool provides comprehensive command execution with:

#### Safety Features
- **Command validation** and safety checks for dangerous operations
- **Interactive confirmation prompts** for destructive operations
- **Working directory context** awareness
- **Timeout protection** with configurable limits
- **Real-time command preview** before execution
- **Detailed command information** on request

#### Execution Modes
- **Sequential**: Commands that depend on previous results
- **Parallel**: Independent operations that can run simultaneously

#### User Confirmation System
When `confirm_commands` is enabled (default), the system will:
1. **Show command preview**: Display exactly what will be executed
2. **Request approval**: Ask for user confirmation with options:
   - `y` or `yes` - Approve and execute
   - `n` or `no` - Deny execution
   - `details` - Show detailed command information
3. **Safe execution**: Only approved commands are executed
4. **Auto-approve mode**: Can be disabled for automated workflows

#### Usage Examples

**File Operations**:
```bash
> Create a backup of my project directory
> List all Python files larger than 1MB
> Clean up old log files older than 30 days
```

**System Administration**:
```bash
> Check system resources and running processes
> Update all packages and restart required services
> Monitor network connections and open ports
```

**Development Tasks**:
```bash
> Set up a new React project with TypeScript
> Run tests and generate coverage report
> Build and deploy the application
```

## Architecture

### Core Components

- **Providers**: LLM provider implementations (Deepseek, Ollama)
- **Tools**: Function calling tools (Shell execution, etc.)
- **UI**: Terminal interface components
- **Session**: Chat history and session management
- **Config**: Configuration management
- **Utils**: Logging, retry logic, error handling

### Directory Structure
```
arien-ai/
├── cmd/                 # CLI commands
├── config/              # Configuration management
├── providers/           # LLM provider implementations
├── tools/               # Function calling tools
├── ui/                  # Terminal UI components
├── session/             # Session management
├── utils/               # Utilities (logging, retry)
├── prompts/             # System prompts
├── main.go              # Application entry point
├── go.mod               # Go module definition
├── install.sh           # Installation script
└── README.md            # This file
```

## Development

### Building from Source
```bash
git clone https://github.com/your-username/arien-ai.git
cd arien-ai
go mod download
go build -o arien-ai
```

### Running Tests
```bash
go test ./...
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Troubleshooting

### Common Issues

**Configuration Problems**:
```bash
# Reset configuration
arien-ai config reset
arien-ai onboarding

# Validate current setup
arien-ai config validate
```

**Provider Connection Issues**:
```bash
# Test Deepseek connection
curl -H "Authorization: Bearer your-api-key" https://api.deepseek.com/chat/completions

# Test Ollama connection
curl http://localhost:11434/api/tags
```

**Permission Issues**:
```bash
# Fix binary permissions
chmod +x /usr/local/bin/arien-ai

# Check installation directory
ls -la /usr/local/bin/arien-ai
```

### Debug Mode
```bash
# Enable debug logging
arien-ai --debug interactive

# Verbose output
arien-ai --verbose config show
```

## License

MIT License - see LICENSE file for details.

## Support

- **Issues**: [GitHub Issues](https://github.com/your-username/arien-ai/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/arien-ai/discussions)
- **Documentation**: [Wiki](https://github.com/your-username/arien-ai/wiki)

## Roadmap

- [ ] Additional LLM providers (OpenAI, Anthropic, etc.)
- [ ] Plugin system for custom tools
- [ ] Web interface
- [ ] Docker support
- [ ] Advanced scripting capabilities
- [ ] Integration with popular development tools
