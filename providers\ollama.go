package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

// OllamaProvider implements the Provider interface for Ollama
type OllamaProvider struct {
	config ProviderConfig
	client *resty.Client
}

// NewOllamaProvider creates a new Ollama provider
func NewOllamaProvider(config ProviderConfig) (*OllamaProvider, error) {
	if config.BaseURL == "" {
		config.BaseURL = "http://localhost:11434"
	}

	client := resty.New()
	client.SetBaseURL(config.BaseURL)
	client.SetHeader("Content-Type", "application/json")
	client.SetTimeout(60 * time.Second) // Ollama can be slower

	return &OllamaProvider{
		config: config,
		client: client,
	}, nil
}

// Name returns the provider name
func (p *OllamaProvider) Name() string {
	return "ollama"
}

// Models returns available models
func (p *OllamaProvider) Models() []string {
	// Try to get models from Ollama API
	resp, err := p.client.R().Get("/api/tags")
	if err != nil {
		// Return default models if API call fails
		return []string{"llama3.2", "llama3.1", "codellama", "mistral", "phi3"}
	}

	var result struct {
		Models []struct {
			Name string `json:"name"`
		} `json:"models"`
	}

	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return []string{"llama3.2", "llama3.1", "codellama", "mistral", "phi3"}
	}

	models := make([]string, len(result.Models))
	for i, model := range result.Models {
		models[i] = model.Name
	}

	return models
}

// Chat sends a chat completion request
func (p *OllamaProvider) Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	if req.Model == "" {
		req.Model = p.config.Model
	}

	// Convert to Ollama format
	ollamaReq := map[string]interface{}{
		"model":    req.Model,
		"messages": req.Messages,
		"stream":   false,
	}

	// Add tools if provided
	if len(req.Tools) > 0 {
		tools := make([]map[string]interface{}, len(req.Tools))
		for i, tool := range req.Tools {
			tools[i] = map[string]interface{}{
				"type": tool.Type,
				"function": map[string]interface{}{
					"name":        tool.Function.Name,
					"description": tool.Function.Description,
					"parameters":  tool.Function.Parameters,
				},
			}
		}
		ollamaReq["tools"] = tools
	}

	if req.Temperature > 0 {
		ollamaReq["options"] = map[string]interface{}{
			"temperature": req.Temperature,
			"num_ctx":     4096,
		}
	}

	resp, err := p.client.R().
		SetContext(ctx).
		SetBody(ollamaReq).
		Post("/api/chat")

	if err != nil {
		return nil, fmt.Errorf("failed to send chat request: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("API error: %s", resp.String())
	}

	// Parse Ollama response
	var ollamaResp struct {
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		Done bool `json:"done"`
	}

	if err := json.Unmarshal(resp.Body(), &ollamaResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Convert to standard format
	result := &ChatResponse{
		ID:      fmt.Sprintf("ollama-%d", time.Now().Unix()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   req.Model,
		Choices: []Choice{
			{
				Index: 0,
				Message: Message{
					Role:    ollamaResp.Message.Role,
					Content: ollamaResp.Message.Content,
				},
				FinishReason: "stop",
			},
		},
	}

	return result, nil
}

// Stream sends a streaming chat completion request
func (p *OllamaProvider) Stream(ctx context.Context, req *ChatRequest) (<-chan *StreamResponse, error) {
	if req.Model == "" {
		req.Model = p.config.Model
	}

	responseChan := make(chan *StreamResponse, 100)

	go func() {
		defer close(responseChan)

		// Convert to Ollama format
		ollamaReq := map[string]interface{}{
			"model":    req.Model,
			"messages": req.Messages,
			"stream":   true,
		}

		if req.Temperature > 0 {
			ollamaReq["options"] = map[string]interface{}{
				"temperature": req.Temperature,
			}
		}

		resp, err := p.client.R().
			SetContext(ctx).
			SetBody(ollamaReq).
			SetDoNotParseResponse(true).
			Post("/api/chat")

		if err != nil {
			responseChan <- &StreamResponse{Error: fmt.Errorf("failed to send stream request: %w", err)}
			return
		}

		if resp.IsError() {
			responseChan <- &StreamResponse{Error: fmt.Errorf("API error: %s", resp.String())}
			return
		}

		defer resp.RawBody().Close()

		// Parse streaming response
		scanner := NewSSEScanner(resp.RawBody())
		for scanner.Scan() {
			line := scanner.Text()
			if line == "" {
				continue
			}

			var ollamaResp struct {
				Message struct {
					Role    string `json:"role"`
					Content string `json:"content"`
				} `json:"message"`
				Done bool `json:"done"`
			}

			if err := json.Unmarshal([]byte(line), &ollamaResp); err != nil {
				continue
			}

			streamResp := &StreamResponse{
				ID:      fmt.Sprintf("ollama-%d", time.Now().Unix()),
				Object:  "chat.completion.chunk",
				Created: time.Now().Unix(),
				Model:   req.Model,
				Choices: []StreamChoice{
					{
						Index: 0,
						Delta: MessageDelta{
							Role:    ollamaResp.Message.Role,
							Content: ollamaResp.Message.Content,
						},
					},
				},
				Done: ollamaResp.Done,
			}

			responseChan <- streamResp

			if ollamaResp.Done {
				return
			}
		}

		if err := scanner.Err(); err != nil {
			responseChan <- &StreamResponse{Error: fmt.Errorf("stream reading error: %w", err)}
		}
	}()

	return responseChan, nil
}

// Validate checks if the provider configuration is valid
func (p *OllamaProvider) Validate() error {
	// Test connection by getting available models
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, err := p.client.R().SetContext(ctx).Get("/api/tags")
	if err != nil {
		return fmt.Errorf("failed to connect to Ollama: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("Ollama API error: %s", resp.String())
	}

	return nil
}
