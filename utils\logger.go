package utils

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"

	"arien-ai/config"
)

var (
	logger *log.Logger
)

// InitLogger initializes the logging system
func InitLogger() error {
	cfg := config.Get()
	
	var writers []io.Writer
	
	// Console output
	if cfg.Logging.Console {
		writers = append(writers, os.Stdout)
	}
	
	// File output
	if cfg.Logging.File != "" {
		// Create log directory if it doesn't exist
		logDir := filepath.Dir(cfg.Logging.File)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			return fmt.Errorf("failed to create log directory: %w", err)
		}
		
		file, err := os.OpenFile(cfg.Logging.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
		if err != nil {
			return fmt.Errorf("failed to open log file: %w", err)
		}
		
		writers = append(writers, file)
	}
	
	if len(writers) == 0 {
		writers = append(writers, os.Stdout)
	}
	
	multiWriter := io.MultiWriter(writers...)
	logger = log.New(multiWriter, "", log.LstdFlags|log.Lshortfile)
	
	return nil
}

// GetLogger returns the logger instance
func GetLogger() *log.Logger {
	if logger == nil {
		// Fallback to default logger
		logger = log.New(os.Stdout, "", log.LstdFlags|log.Lshortfile)
	}
	return logger
}

// LogInfo logs an info message
func LogInfo(format string, args ...interface{}) {
	GetLogger().Printf("[INFO] "+format, args...)
}

// LogError logs an error message
func LogError(format string, args ...interface{}) {
	GetLogger().Printf("[ERROR] "+format, args...)
}

// LogDebug logs a debug message
func LogDebug(format string, args ...interface{}) {
	cfg := config.Get()
	if cfg.Logging.Level == "debug" {
		GetLogger().Printf("[DEBUG] "+format, args...)
	}
}

// LogWarn logs a warning message
func LogWarn(format string, args ...interface{}) {
	GetLogger().Printf("[WARN] "+format, args...)
}
