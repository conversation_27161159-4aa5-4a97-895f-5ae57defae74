package main

import (
	"fmt"
	"os"

	"arien-ai/cmd"
	"arien-ai/config"
	"arien-ai/utils"

	"github.com/spf13/cobra"
)

var (
	version = "1.0.0"
	commit  = "dev"
	date    = "unknown"
)

func main() {
	// Initialize configuration
	if err := config.Initialize(); err != nil {
		fmt.Fprintf(os.Stderr, "Error initializing configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	utils.InitLogger()

	// Create root command
	rootCmd := &cobra.Command{
		Use:   "arien-ai",
		Short: "Arien AI - Modern CLI Terminal with LLM Integration",
		Long: `Arien AI is a powerful CLI terminal system that integrates with LLM providers
to execute shell commands intelligently and complete user tasks.

Features:
- Function calling with shell command execution
- Support for Deepseek and Ollama providers
- Interactive terminal interface with real-time responses
- Comprehensive error handling and retry logic
- Session management and chat history
- Cross-platform support (Windows WSL, macOS, Linux)`,
		Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
	}

	// Add commands
	rootCmd.AddCommand(cmd.NewInteractiveCmd())
	rootCmd.AddCommand(cmd.NewConfigCmd())
	rootCmd.AddCommand(cmd.NewSessionCmd())
	rootCmd.AddCommand(cmd.NewOnboardingCmd())

	// Global flags
	rootCmd.PersistentFlags().StringP("config", "c", "", "config file (default is $HOME/.arien-ai.yaml)")
	rootCmd.PersistentFlags().BoolP("verbose", "v", false, "verbose output")
	rootCmd.PersistentFlags().BoolP("debug", "d", false, "debug mode")

	// Execute root command
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
