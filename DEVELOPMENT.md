# Arien AI Development Guide

## Project Overview

Arien AI is a modern CLI terminal system built in Go 1.24.4 that integrates with LLM providers (Deepseek and Ollama) to execute shell commands intelligently through function calling.

## Architecture

### Core Components

1. **Main Application** (`main.go`)
   - Entry point with CLI command routing
   - Version information and global flags

2. **Configuration Management** (`config/`)
   - YAML-based configuration system
   - Provider settings, session config, terminal preferences
   - Automatic defaults and validation

3. **LLM Providers** (`providers/`)
   - Abstract provider interface
   - Deepseek API integration with streaming support
   - Ollama local model integration
   - Unified chat completion and streaming APIs

4. **Function Calling Tools** (`tools/`)
   - Shell command execution tool with comprehensive safety features
   - Tool manager for registering and executing tools
   - Detailed usage guidelines and examples

5. **Terminal UI** (`ui/`)
   - Interactive terminal interface using Bubble Tea
   - Real-time chat with thinking animations
   - Message history and session management
   - Slash commands for configuration

6. **Session Management** (`session/`)
   - Persistent chat history
   - Session creation, loading, and management
   - JSON-based storage with metadata

7. **Utilities** (`utils/`)
   - Structured logging system
   - Retry logic with exponential backoff
   - Error handling and recovery

### Key Features Implemented

#### 🚀 LLM Integration
- **Deepseek Provider**: Full API integration with `deepseek-chat` and `deepseek-reasoner` models
- **Ollama Provider**: Local model support with automatic model detection
- **Streaming Support**: Real-time response streaming for both providers
- **Function Calling**: Comprehensive tool integration with detailed descriptions

#### 🛠️ Shell Command Tool
- **Safety Features**: Command validation, dangerous command detection
- **Execution Modes**: Sequential and parallel execution strategies
- **Comprehensive Documentation**: Detailed usage guidelines with examples
- **Error Handling**: Robust error detection and recovery
- **Cross-Platform**: Windows, macOS, and Linux support

#### 🎨 Terminal Interface
- **Interactive UI**: Full-featured terminal using Bubble Tea framework
- **Thinking Animation**: Visual feedback with ball animation during AI processing
- **Message History**: Persistent conversation tracking with timestamps
- **Slash Commands**: `/help`, `/model`, `/provider`, `/session`, `/clear`, `/config`, `/quit`
- **Responsive Design**: Adaptive layout with proper text wrapping

#### 📊 Session Management
- **Persistent Storage**: JSON-based session storage in `~/.arien-ai/sessions/`
- **Session Operations**: Create, load, list, delete sessions
- **Metadata Tracking**: Provider, model, creation/update times
- **Message Limits**: Configurable history limits

#### ⚙️ Configuration System
- **YAML Configuration**: Structured configuration in `~/.arien-ai.yaml`
- **Environment Support**: Multiple configuration sources
- **Validation**: Configuration validation and testing
- **Defaults**: Sensible default values for all settings

## Installation Methods

### 1. Cross-Platform Installation Script
- **Single Script**: `install.sh` supports Windows WSL, macOS, and Linux
- **Multiple Options**: Binary download or source compilation
- **Update/Uninstall**: Built-in update and uninstall functionality
- **Dependency Checking**: Automatic dependency validation

### 2. Manual Installation
- **Go Build**: Standard Go build process with version embedding
- **Makefile**: Comprehensive build targets and development tools
- **Docker**: Multi-stage Docker build with security scanning

### 3. Package Managers (Future)
- Ready for distribution via Homebrew, APT, etc.

## Development Workflow

### Building
```bash
# Standard build
make build

# Build for all platforms
make build-all

# Development build with debug info
make dev-run
```

### Testing
```bash
# Run all tests
make test

# Run tests with coverage
make test-coverage

# Code quality checks
make check
```

### Development Setup
```bash
# Install development dependencies
make dev-setup

# Format and lint code
make fmt lint vet
```

## System Prompt Integration

### Comprehensive AI Guidelines
- **Safety-First Approach**: Detailed safety guidelines for command execution
- **Usage Patterns**: When to use parallel vs sequential execution
- **Error Handling**: Comprehensive error recovery strategies
- **Best Practices**: Command examples and safety considerations

### Function Tool Documentation
- **Detailed Descriptions**: Comprehensive tool usage guidelines
- **Safety Considerations**: Command validation and approval workflows
- **Examples**: Real-world usage patterns and best practices
- **Error Recovery**: Intelligent retry and recovery strategies

## Security Features

### Command Safety
- **Validation**: Pre-execution command validation
- **Dangerous Command Detection**: Built-in dangerous command patterns
- **Confirmation Prompts**: User approval for potentially destructive operations
- **Working Directory Awareness**: Context-aware command execution

### Configuration Security
- **API Key Masking**: Sensitive information protection in logs and displays
- **File Permissions**: Secure configuration file handling
- **Input Validation**: Comprehensive input sanitization

## Error Handling & Retry Logic

### Intelligent Retry System
- **Exponential Backoff**: Smart retry delays with jitter
- **Error Classification**: Retryable vs non-retryable error detection
- **Network Resilience**: Automatic recovery from network issues
- **Rate Limit Handling**: Intelligent rate limit detection and backoff

### User-Friendly Error Messages
- **Clear Feedback**: Descriptive error messages with suggested solutions
- **Context Preservation**: Error context for debugging
- **Recovery Suggestions**: Actionable recovery recommendations

## CI/CD Pipeline

### GitHub Actions Workflow
- **Multi-Platform Testing**: Linux, macOS, Windows testing
- **Security Scanning**: Gosec and Trivy vulnerability scanning
- **Docker Integration**: Multi-architecture Docker builds
- **Automated Releases**: Binary releases with checksums

### Quality Assurance
- **Code Coverage**: Comprehensive test coverage reporting
- **Linting**: golangci-lint integration
- **Security**: Automated security vulnerability scanning
- **Documentation**: Automated documentation generation

## Future Enhancements

### Planned Features
1. **Additional Providers**: OpenAI, Anthropic, Google AI integration
2. **Plugin System**: Custom tool development framework
3. **Web Interface**: Browser-based terminal interface
4. **Advanced Scripting**: Workflow automation capabilities
5. **Integration APIs**: REST API for external integrations

### Performance Optimizations
1. **Caching**: Response caching for repeated operations
2. **Streaming Optimization**: Enhanced streaming performance
3. **Memory Management**: Optimized memory usage for large sessions
4. **Concurrent Processing**: Enhanced parallel execution capabilities

## Contributing

### Development Standards
- **Go Standards**: Follow Go best practices and conventions
- **Testing**: Comprehensive test coverage for all new features
- **Documentation**: Clear documentation for all public APIs
- **Security**: Security-first development approach

### Code Review Process
- **Pull Requests**: All changes via pull requests
- **Automated Testing**: CI/CD pipeline validation
- **Security Review**: Security implications assessment
- **Performance Impact**: Performance impact evaluation

## Deployment

### Production Considerations
- **Configuration Management**: Environment-specific configurations
- **Logging**: Structured logging for production monitoring
- **Monitoring**: Health checks and metrics collection
- **Security**: Production security hardening

### Scaling
- **Resource Management**: Efficient resource utilization
- **Session Storage**: Scalable session storage solutions
- **Provider Management**: Load balancing across providers
- **Rate Limiting**: Built-in rate limiting and throttling

This development guide provides a comprehensive overview of the Arien AI system architecture, implementation details, and development workflow. The system is designed to be robust, secure, and extensible while providing an excellent user experience for AI-powered terminal operations.
