# Arien AI Makefile

# Variables
BINARY_NAME=arien-ai
VERSION?=1.0.0
COMMIT?=$(shell git rev-parse --short HEAD 2>/dev/null || echo "dev")
DATE?=$(shell date -u +%Y-%m-%dT%H:%M:%SZ)
LDFLAGS=-ldflags "-X main.version=$(VERSION) -X main.commit=$(COMMIT) -X main.date=$(DATE)"

# Build targets
.PHONY: all build clean test install uninstall deps fmt lint vet check

all: build

# Build the binary
build:
	@echo "Building $(BINARY_NAME)..."
	go build $(LDFLAGS) -o $(BINARY_NAME) .

# Build for multiple platforms
build-all:
	@echo "Building for multiple platforms..."
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o dist/$(BINARY_NAME)-linux-amd64 .
	GOOS=linux GOARCH=arm64 go build $(LDFLAGS) -o dist/$(BINARY_NAME)-linux-arm64 .
	GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o dist/$(BINARY_NAME)-darwin-amd64 .
	GOOS=darwin GOARCH=arm64 go build $(LDFLAGS) -o dist/$(BINARY_NAME)-darwin-arm64 .
	GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o dist/$(BINARY_NAME)-windows-amd64.exe .

# Install dependencies
deps:
	@echo "Installing dependencies..."
	go mod download
	go mod tidy

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Lint code
lint:
	@echo "Linting code..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed, skipping..."; \
	fi

# Vet code
vet:
	@echo "Vetting code..."
	go vet ./...

# Run tests
test:
	@echo "Running tests..."
	go test -v ./...

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Check code quality
check: fmt vet lint test

# Clean build artifacts
clean:
	@echo "Cleaning..."
	rm -f $(BINARY_NAME)
	rm -rf dist/
	rm -f coverage.out coverage.html

# Install binary to system
install: build
	@echo "Installing $(BINARY_NAME) to /usr/local/bin..."
	@if [ -w /usr/local/bin ]; then \
		cp $(BINARY_NAME) /usr/local/bin/; \
	else \
		sudo cp $(BINARY_NAME) /usr/local/bin/; \
	fi
	@chmod +x /usr/local/bin/$(BINARY_NAME)
	@echo "Installation complete!"

# Uninstall binary from system
uninstall:
	@echo "Uninstalling $(BINARY_NAME)..."
	@if [ -f /usr/local/bin/$(BINARY_NAME) ]; then \
		if [ -w /usr/local/bin ]; then \
			rm /usr/local/bin/$(BINARY_NAME); \
		else \
			sudo rm /usr/local/bin/$(BINARY_NAME); \
		fi; \
		echo "Uninstallation complete!"; \
	else \
		echo "$(BINARY_NAME) not found in /usr/local/bin"; \
	fi

# Development setup
dev-setup:
	@echo "Setting up development environment..."
	go mod download
	@if ! command -v golangci-lint >/dev/null 2>&1; then \
		echo "Installing golangci-lint..."; \
		curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin v1.54.2; \
	fi

# Run the application in development mode
dev-run: build
	./$(BINARY_NAME) --debug

# Create release
release: clean check build-all
	@echo "Creating release $(VERSION)..."
	@mkdir -p dist
	@for file in dist/*; do \
		if [ -f "$$file" ]; then \
			echo "Created: $$file"; \
		fi; \
	done

# Docker build
docker-build:
	@echo "Building Docker image..."
	docker build -t arien-ai:$(VERSION) .
	docker tag arien-ai:$(VERSION) arien-ai:latest

# Show help
help:
	@echo "Available targets:"
	@echo "  build         - Build the binary"
	@echo "  build-all     - Build for multiple platforms"
	@echo "  deps          - Install dependencies"
	@echo "  fmt           - Format code"
	@echo "  lint          - Lint code"
	@echo "  vet           - Vet code"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  check         - Run all code quality checks"
	@echo "  clean         - Clean build artifacts"
	@echo "  install       - Install binary to system"
	@echo "  uninstall     - Uninstall binary from system"
	@echo "  dev-setup     - Set up development environment"
	@echo "  dev-run       - Run in development mode"
	@echo "  release       - Create release build"
	@echo "  docker-build  - Build Docker image"
	@echo "  help          - Show this help"
