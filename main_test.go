package main

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"arien-ai/config"
	"arien-ai/prompts"
	"arien-ai/providers"
	"arien-ai/session"
	"arien-ai/tools"
	"arien-ai/ui"
	"arien-ai/utils"
)

func TestMain(m *testing.M) {
	// Setup test environment
	os.Setenv("ARIEN_AI_CONFIG_DIR", "/tmp/arien-ai-test")
	
	// Run tests
	code := m.Run()
	
	// Cleanup
	os.RemoveAll("/tmp/arien-ai-test")
	
	os.Exit(code)
}

func TestConfigInitialization(t *testing.T) {
	err := config.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize config: %v", err)
	}
	
	cfg := config.Get()
	if cfg == nil {
		t.Fatal("Config is nil")
	}
	
	// Test default values
	if cfg.Provider.Name != "deepseek" {
		t.<PERSON>("Expected default provider 'deepseek', got '%s'", cfg.Provider.Name)
	}
	
	if cfg.Session.MaxHistory != 1000 {
		t.<PERSON>("Expected default max history 1000, got %d", cfg.Session.MaxHistory)
	}
}

func TestProviderCreation(t *testing.T) {
	tests := []struct {
		name     string
		provider string
		wantErr  bool
	}{
		{
			name:     "Valid Deepseek provider",
			provider: "deepseek",
			wantErr:  false,
		},
		{
			name:     "Valid Ollama provider",
			provider: "ollama",
			wantErr:  false,
		},
		{
			name:     "Invalid provider",
			provider: "invalid",
			wantErr:  true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := providers.ProviderConfig{
				Name:    tt.provider,
				Model:   "test-model",
				APIKey:  "test-key",
				BaseURL: "http://localhost:8080",
			}
			
			_, err := providers.NewProvider(config)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewProvider() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestShellTool(t *testing.T) {
	tool := tools.NewShellTool()
	
	if tool.Name() != "execute_shell_command" {
		t.Errorf("Expected tool name 'execute_shell_command', got '%s'", tool.Name())
	}
	
	// Test tool definition
	def := tool.GetDefinition()
	if def.Type != "function" {
		t.Errorf("Expected tool type 'function', got '%s'", def.Type)
	}
	
	if def.Function.Name != "execute_shell_command" {
		t.Errorf("Expected function name 'execute_shell_command', got '%s'", def.Function.Name)
	}
}

func TestShellToolExecution(t *testing.T) {
	tool := tools.NewShellTool()
	ctx := context.Background()
	
	// Test simple command
	args := `{"command": "echo hello", "timeout": 5}`
	result, err := tool.Execute(ctx, args)
	
	if err != nil {
		t.Fatalf("Failed to execute command: %v", err)
	}
	
	if !result.Success {
		t.Errorf("Command execution failed: %s", result.Error)
	}
	
	if result.Output == "" {
		t.Error("Expected output, got empty string")
	}
}

func TestCommandValidation(t *testing.T) {
	tests := []struct {
		name    string
		command string
		wantErr bool
	}{
		{
			name:    "Valid command",
			command: "ls -la",
			wantErr: false,
		},
		{
			name:    "Empty command",
			command: "",
			wantErr: true,
		},
		{
			name:    "Dangerous command",
			command: "rm -rf /",
			wantErr: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tools.ValidateCommand(tt.command)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateCommand() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSessionManagement(t *testing.T) {
	manager, err := session.NewManager()
	if err != nil {
		t.Fatalf("Failed to create session manager: %v", err)
	}
	
	// Create new session
	sess, err := manager.NewSession("Test Session", "deepseek", "deepseek-chat")
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}
	
	if sess.Name != "Test Session" {
		t.Errorf("Expected session name 'Test Session', got '%s'", sess.Name)
	}
	
	if sess.Metadata.Provider != "deepseek" {
		t.Errorf("Expected provider 'deepseek', got '%s'", sess.Metadata.Provider)
	}
	
	// Add message
	msg := session.Message{
		Role:      "user",
		Content:   "Hello",
		Timestamp: time.Now(),
	}
	sess.AddMessage(msg)
	
	if len(sess.Messages) != 1 {
		t.Errorf("Expected 1 message, got %d", len(sess.Messages))
	}
	
	// Save and load session
	err = manager.SaveSession(sess)
	if err != nil {
		t.Fatalf("Failed to save session: %v", err)
	}
	
	loadedSess, err := manager.LoadSession(sess.ID)
	if err != nil {
		t.Fatalf("Failed to load session: %v", err)
	}
	
	if loadedSess.ID != sess.ID {
		t.Errorf("Expected session ID '%s', got '%s'", sess.ID, loadedSess.ID)
	}
	
	if len(loadedSess.Messages) != 1 {
		t.Errorf("Expected 1 message in loaded session, got %d", len(loadedSess.Messages))
	}
}

func TestToolManager(t *testing.T) {
	tm := tools.NewToolManager()
	
	// Check if shell tool is registered
	tool, exists := tm.GetTool("execute_shell_command")
	if !exists {
		t.Error("Shell tool not registered")
	}
	
	if tool.Name() != "execute_shell_command" {
		t.Errorf("Expected tool name 'execute_shell_command', got '%s'", tool.Name())
	}
	
	// Test tool definitions
	definitions := tm.GetToolDefinitions()
	if len(definitions) == 0 {
		t.Error("No tool definitions found")
	}
	
	found := false
	for _, def := range definitions {
		if def.Function.Name == "execute_shell_command" {
			found = true
			break
		}
	}
	
	if !found {
		t.Error("Shell tool definition not found")
	}
}

func TestProviderModels(t *testing.T) {
	// Test Deepseek models
	deepseekModels := providers.GetProviderModels("deepseek")
	if len(deepseekModels) == 0 {
		t.Error("No Deepseek models found")
	}
	
	expectedModels := []string{"deepseek-chat", "deepseek-reasoner"}
	for _, expected := range expectedModels {
		found := false
		for _, model := range deepseekModels {
			if model == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected model '%s' not found in Deepseek models", expected)
		}
	}
	
	// Test Ollama models
	ollamaModels := providers.GetProviderModels("ollama")
	if len(ollamaModels) == 0 {
		t.Error("No Ollama models found")
	}
}

func TestAvailableProviders(t *testing.T) {
	providers := providers.GetAvailableProviders()
	if len(providers) == 0 {
		t.Error("No providers found")
	}

	expectedProviders := []string{"deepseek", "ollama"}
	for _, expected := range expectedProviders {
		found := false
		for _, provider := range providers {
			if provider == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected provider '%s' not found", expected)
		}
	}
}

func TestUIComponents(t *testing.T) {
	styles := ui.NewStyles()

	// Test header component
	header := ui.NewHeaderComponent(styles)
	if header == nil {
		t.Fatal("Failed to create header component")
	}

	// Test thinking component
	thinking := ui.NewThinkingComponent(styles)
	if thinking == nil {
		t.Fatal("Failed to create thinking component")
	}

	// Test thinking animation
	thinking.NextFrame()
	thinkingOutput := thinking.Render("Testing...")
	if thinkingOutput == "" {
		t.Error("Thinking component returned empty output")
	}

	// Test slash commands component
	slashCommands := ui.NewSlashCommandsComponent(styles)
	if slashCommands == nil {
		t.Fatal("Failed to create slash commands component")
	}

	helpText := slashCommands.GetHelpText()
	if helpText == "" {
		t.Error("Slash commands help text is empty")
	}
}

func TestSystemPrompt(t *testing.T) {
	// Initialize config first
	err := config.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize config: %v", err)
	}

	systemPrompt, err := prompts.NewSystemPrompt()
	if err != nil {
		t.Fatalf("Failed to create system prompt: %v", err)
	}

	// Test system message generation
	systemMsg := systemPrompt.GetSystemMessage()
	if systemMsg.Role != "system" {
		t.Errorf("Expected system message role 'system', got '%s'", systemMsg.Role)
	}

	if systemMsg.Content == "" {
		t.Error("System message content is empty")
	}

	// Test tool instructions
	toolInstructions := systemPrompt.GetToolInstructions()
	if toolInstructions == "" {
		t.Error("Tool instructions are empty")
	}

	// Test prompt stats
	stats := systemPrompt.GetPromptStats()
	if stats == nil {
		t.Error("Prompt stats are nil")
	}

	if stats["provider"] == nil {
		t.Error("Provider not found in prompt stats")
	}
}

func TestRetryLogic(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Test successful retry with a retryable error
	attempts := 0
	successFunc := func() error {
		attempts++
		if attempts < 3 {
			// Create a retryable error (network timeout simulation)
			return fmt.Errorf("connection timeout")
		}
		return nil
	}

	err := utils.Retry(ctx, successFunc)
	if err != nil {
		t.Errorf("Retry failed: %v", err)
	}

	if attempts != 3 {
		t.Errorf("Expected 3 attempts, got %d", attempts)
	}

	// Test non-retryable error
	nonRetryableFunc := func() error {
		return fmt.Errorf("invalid argument")
	}

	err = utils.Retry(ctx, nonRetryableFunc)
	if err == nil {
		t.Error("Expected non-retryable error to fail immediately")
	}
}

func TestSlashCommands(t *testing.T) {
	styles := ui.NewStyles()
	slashCommands := ui.NewSlashCommandsComponent(styles)

	// Create a test session
	manager, err := session.NewManager()
	if err != nil {
		t.Fatalf("Failed to create session manager: %v", err)
	}

	sess, err := manager.NewSession("Test", "deepseek", "deepseek-chat")
	if err != nil {
		t.Fatalf("Failed to create test session: %v", err)
	}

	// Test help command
	result, err := slashCommands.ProcessCommand("/help", sess)
	if err != nil {
		t.Errorf("Help command failed: %v", err)
	}
	if result == "" {
		t.Error("Help command returned empty result")
	}

	// Test config command
	result, err = slashCommands.ProcessCommand("/config", sess)
	if err != nil {
		t.Errorf("Config command failed: %v", err)
	}
	if result == "" {
		t.Error("Config command returned empty result")
	}

	// Test clear command
	sess.AddMessage(session.Message{Role: "user", Content: "test", Timestamp: time.Now()})
	result, err = slashCommands.ProcessCommand("/clear", sess)
	if err != nil {
		t.Errorf("Clear command failed: %v", err)
	}
	if len(sess.GetMessages()) != 0 {
		t.Error("Clear command did not clear messages")
	}

	// Test unknown command
	_, err = slashCommands.ProcessCommand("/unknown", sess)
	if err == nil {
		t.Error("Expected error for unknown command")
	}
}
